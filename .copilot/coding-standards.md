# MCDN 代码规范和风格指南

## 基础代码风格

项目遵循以下代码风格和最佳实践：

1. 使用 `urfave/cli/v2` 构建命令行应用
2. 使用 `viper` 进行配置管理
3. 使用 `sync.Once` 和 `sync.RWMutex` 实现配置的懒加载和线程安全
4. 遵循 Go 语言的标准命名约定
5. 使用结构体标签进行配置映射（`mapstructure` 和 `json`）

## Kubernetes 风格的注解命名规范

项目严格遵循 Kubernetes 云原生生态的注解命名最佳实践：

### 注解命名格式

**标准格式**：`域名前缀/功能名称`

```go
const (
    // AnnotationPrefix 注解前缀，使用正向域名格式
    AnnotationPrefix = "mcdn.lilithgames.com"
    
    // 功能相关注解 - 符合 K8s 云原生风格
    AnnotationImportSource   = AnnotationPrefix + "/import-source"   // 导入来源
    AnnotationImportTime     = AnnotationPrefix + "/import-time"     // 导入时间
    AnnotationOriginalID     = AnnotationPrefix + "/original-id"     // 原始ID
    AnnotationMigrationNotes = AnnotationPrefix + "/migration-notes" // 迁移备注
    AnnotationManagedBy      = AnnotationPrefix + "/managed-by"      // 管理者
    AnnotationCreatedBy      = AnnotationPrefix + "/created-by"      // 创建者
)
```

### 命名原则

1. **域名前缀在前**：`mcdn.lilithgames.com/功能名`，而不是 `功能名.mcdn.lilithgames.com`
2. **统一分隔符**：使用 `/` 作为域名和功能的分隔符，使用 `-` 作为单词分隔符
3. **简洁明了**：直接功能命名，避免过度嵌套层级
4. **一致性**：与 `kubernetes.io/arch`、`prometheus.io/scrape` 等官方注解保持风格一致

### 反面示例（避免使用）

```go
// ❌ 错误：域名前缀位置错误
AnnotationImportSource = "migration.mcdn.lilithgames.com/import-source"

// ❌ 错误：混合分隔符使用
AnnotationImportSource = "mcdn.lilithgames.com.migration/import-source"

// ❌ 错误：过度嵌套
AnnotationImportSource = "mcdn.lilithgames.com/migration/import/source/legacy"
```

### 正确示例（推荐使用）

```go
// ✅ 正确：简洁的功能命名
AnnotationImportSource = "mcdn.lilithgames.com/import-source"

// ✅ 正确：清晰的语义表达
AnnotationOriginalID = "mcdn.lilithgames.com/original-id"

// ✅ 正确：一致的命名风格
AnnotationManagedBy = "mcdn.lilithgames.com/managed-by"
```

## ResourceMetadata 设计规范

项目的资源元数据设计遵循 Kubernetes API 对象的最佳实践：

### 核心字段设计

```go
type ResourceMetadata struct {
    // 基础标识字段
    Name      string            `json:"name" yaml:"name"`
    Namespace string            `json:"namespace,omitempty" yaml:"namespace,omitempty"`
    
    // 分类和选择字段
    Labels      map[string]string `json:"labels,omitempty" yaml:"labels,omitempty"`
    Annotations map[string]string `json:"annotations,omitempty" yaml:"annotations,omitempty"`
    
    // 生命周期管理字段
    CreationTimestamp *time.Time `json:"creationTimestamp,omitempty" yaml:"creationTimestamp,omitempty"`
    DeletionTimestamp *time.Time `json:"deletionTimestamp,omitempty" yaml:"deletionTimestamp,omitempty"`
    Finalizers        []string   `json:"finalizers,omitempty" yaml:"finalizers,omitempty"`
}
```

### 字段用途和最佳实践

1. **Labels vs Annotations 使用原则**：

   - **Labels**：用于资源分类和选择，支持查询过滤
   - **Annotations**：用于存储非识别性元数据，支持临时标记和工具集成

2. **Annotations 典型应用场景**：

   - 导入存量数据时的来源标记
   - 工具集成的临时配置信息
   - 迁移过程中的状态记录
   - 外部系统的关联信息

3. **生命周期管理**：

   - **CreationTimestamp**：资源创建时间，便于审计
   - **DeletionTimestamp**：删除时间戳，支持优雅删除流程
   - **Finalizers**：终结器列表，确保资源清理完整性

### 辅助方法最佳实践

项目提供了丰富的辅助方法来操作 ResourceMetadata：

```go
// 注解操作
func (m *ResourceMetadata) GetAnnotation(key string) (string, bool)
func (m *ResourceMetadata) SetAnnotation(key, value string)
func (m *ResourceMetadata) DeleteAnnotation(key string)

// 标签操作  
func (m *ResourceMetadata) GetLabel(key string) (string, bool)
func (m *ResourceMetadata) SetLabel(key, value string)
func (m *ResourceMetadata) DeleteLabel(key string)

// 生命周期管理
func (m *ResourceMetadata) IsBeingDeleted() bool
func (m *ResourceMetadata) HasFinalizer(finalizer string) bool
func (m *ResourceMetadata) AddFinalizer(finalizer string)
func (m *ResourceMetadata) RemoveFinalizer(finalizer string)

// 导入场景专用方法
func (m *ResourceMetadata) SetImportMetadata(source, originalID, notes string)
func (m *ResourceMetadata) GetImportSource() (string, bool)
func (m *ResourceMetadata) GetOriginalID() (string, bool)
```

### 使用示例

```go
// 创建资源元数据
metadata := &ResourceMetadata{
    Name: "example-route",
    Labels: map[string]string{
        "app":     "mcdn",
        "version": "v1.0",
    },
}

// 设置导入相关信息
metadata.SetImportMetadata(
    "legacy-cdn-system", 
    "route-123456", 
    "从旧系统迁移的路由配置"
)

// 设置管理信息
metadata.SetAnnotation(AnnotationManagedBy, "migration-tool-v2.0")

// 添加终结器确保清理
metadata.AddFinalizer("mcdn.lilithgames.com/cleanup-cdn")

// 检查资源状态
if metadata.IsBeingDeleted() {
    // 处理删除逻辑
    if metadata.HasFinalizer("mcdn.lilithgames.com/cleanup-cdn") {
        // 执行清理操作
        // ...
        metadata.RemoveFinalizer("mcdn.lilithgames.com/cleanup-cdn")
    }
}
```

## 错误处理最佳实践

### 统一的错误处理

```go
func (c *Controller) wrapError(operation string, err error) error {
    if err == nil {
        return nil
    }
    return fmt.Errorf("%s失败: %w", operation, err)
}
```

### 结构化日志记录

```go
func (c *Controller) logError(operation string, err error, fields ...any) {
    args := append([]any{"operation", operation, "error", err}, fields...)
    c.log.Error("操作失败", args...)
}
```

这些设计原则确保了：

- **兼容性**：与 Kubernetes 生态完全兼容
- **可扩展性**：支持未来功能的无缝扩展
- **易维护性**：清晰的命名和结构化的设计
- **工具友好**：支持标准的 K8s 工具链集成
