package vendor

import (
	"context"

	apiv1 "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/api/v1"
)

// Client 定义云厂商客户端接口
type Client interface {
	// DNS 相关方法
	CreateDNSZone(ctx context.Context, domain string, config map[string]any) (string, error)
	DeleteDNSZone(ctx context.Context, zoneID string) error
	GetDNSZone(ctx context.Context, zoneID string) (*DNSZoneInfo, error)
	ListDNSZones(ctx context.Context) ([]*DNSZoneInfo, error)

	CreateDNSRecord(ctx context.Context, zoneID string, record *DNSRecordRequest) (string, error)
	UpdateDNSRecord(ctx context.Context, recordID string, record *DNSRecordRequest) error
	DeleteDNSRecord(ctx context.Context, recordID string) error
	GetDNSRecord(ctx context.Context, recordID string) (*DNSRecordInfo, error)
	ListDNSRecords(ctx context.Context, zoneID string) ([]*DNSRecordInfo, error)

	// CDN 相关方法
	CreateDistribution(ctx context.Context, config *DistributionRequest) (string, error)
	UpdateDistribution(ctx context.Context, distributionID string, config *DistributionRequest) error
	DeleteDistribution(ctx context.Context, distributionID string) error
	GetDistribution(ctx context.Context, distributionID string) (*DistributionInfo, error)
	ListDistributions(ctx context.Context) ([]*DistributionInfo, error)

	// 通用方法
	GetVendorType() apiv1.VendorType
	ValidateConfig(config map[string]any) error
}

// DNSClient 定义DNS厂商客户端接口
type DNSClient interface {
	// DNS 相关方法
	CreateDNSZone(ctx context.Context, domain string, config map[string]any) (string, error)
	DeleteDNSZone(ctx context.Context, zoneID string) error
	GetDNSZone(ctx context.Context, zoneID string) (*DNSZoneInfo, error)
	ListDNSZones(ctx context.Context) ([]*DNSZoneInfo, error)

	CreateDNSRecord(ctx context.Context, zoneID string, record *DNSRecordRequest) (string, error)
	UpdateDNSRecord(ctx context.Context, recordID string, record *DNSRecordRequest) error
	DeleteDNSRecord(ctx context.Context, recordID string) error
	GetDNSRecord(ctx context.Context, recordID string) (*DNSRecordInfo, error)
	ListDNSRecords(ctx context.Context, zoneID string) ([]*DNSRecordInfo, error)

	// DNS厂商特定方法
	GetVendorType() apiv1.VendorType
	ValidateConfig(config map[string]any) error
}

// CDNClient 定义CDN厂商客户端接口
type CDNClient interface {
	// CDN 相关方法
	CreateDistribution(ctx context.Context, config *DistributionRequest) (string, error)
	UpdateDistribution(ctx context.Context, distributionID string, config *DistributionRequest) error
	DeleteDistribution(ctx context.Context, distributionID string) error
	GetDistribution(ctx context.Context, distributionID string) (*DistributionInfo, error)
	ListDistributions(ctx context.Context) ([]*DistributionInfo, error)

	// CDN厂商特定方法
	GetVendorType() apiv1.VendorType
	ValidateConfig(config map[string]any) error
}

// Manager 定义云厂商管理器接口
type Manager interface {
	// DNS厂商相关方法
	RegisterDNSClient(vendorType apiv1.VendorType, client DNSClient) error
	GetDNSClient(vendorType apiv1.VendorType) (DNSClient, error)
	ListDNSClients() map[apiv1.VendorType]DNSClient

	// CDN厂商相关方法
	RegisterCDNClient(vendorType apiv1.VendorType, client CDNClient) error
	GetCDNClient(vendorType apiv1.VendorType) (CDNClient, error)
	ListCDNClients() map[apiv1.VendorType]CDNClient

	// 调度相关方法
	SwitchDistribution(ctx context.Context, distributionName string, params map[string]any) error
	SwitchCDN(ctx context.Context, cdnProvider string, params map[string]any) error

	// 兼容旧接口
	RegisterClient(vendorType apiv1.VendorType, client Client) error
	GetClient(vendorType apiv1.VendorType) (Client, error)
	ListClients() map[apiv1.VendorType]Client
}

// DNSZoneInfo 定义 DNS Zone 信息
type DNSZoneInfo struct {
	ID          string   `json:"id"`
	Domain      string   `json:"domain"`
	Status      string   `json:"status"`
	NameServers []string `json:"nameServers"`
	TTL         int32    `json:"ttl"`
	CreatedAt   string   `json:"createdAt"`
	UpdatedAt   string   `json:"updatedAt"`
}

// DNSRecordRequest 定义 DNS Record 请求
type DNSRecordRequest struct {
	Name     string `json:"name"`
	Type     string `json:"type"`
	Value    string `json:"value"`
	TTL      int32  `json:"ttl"`
	Priority int32  `json:"priority,omitempty"`
	Weight   int32  `json:"weight,omitempty"`
}

// DNSRecordInfo 定义 DNS Record 信息
type DNSRecordInfo struct {
	ID       string `json:"id"`
	ZoneID   string `json:"zoneId"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	Value    string `json:"value"`
	TTL      int32  `json:"ttl"`
	Priority int32  `json:"priority,omitempty"`
	Weight   int32  `json:"weight,omitempty"`
	Status   string `json:"status"`
}

// DistributionRequest 定义 CDN Distribution 请求
type DistributionRequest struct {
	Domain      string          `json:"domain"`
	Origins     []OriginRequest `json:"origins"`
	CacheConfig map[string]any  `json:"cacheConfig,omitempty"`
	SSLConfig   map[string]any  `json:"sslConfig,omitempty"`
	Enabled     bool            `json:"enabled"`
	Comment     string          `json:"comment,omitempty"`
}

// OriginRequest 定义源站请求
type OriginRequest struct {
	ID                   string `json:"id"`
	DomainName           string `json:"domainName"`
	OriginPath           string `json:"originPath,omitempty"`
	HTTPPort             int32  `json:"httpPort,omitempty"`
	HTTPSPort            int32  `json:"httpsPort,omitempty"`
	OriginProtocolPolicy string `json:"originProtocolPolicy,omitempty"`
	Weight               int32  `json:"weight,omitempty"`
	Priority             int32  `json:"priority,omitempty"`
}

// DistributionInfo 定义 CDN Distribution 信息
type DistributionInfo struct {
	ID         string         `json:"id"`
	Domain     string         `json:"domain"`
	DomainName string         `json:"domainName"`
	Status     string         `json:"status"`
	Enabled    bool           `json:"enabled"`
	Origins    []OriginInfo   `json:"origins"`
	Config     map[string]any `json:"config,omitempty"`
	CreatedAt  string         `json:"createdAt"`
	UpdatedAt  string         `json:"updatedAt"`
}

// OriginInfo 定义源站信息
type OriginInfo struct {
	ID                   string `json:"id"`
	DomainName           string `json:"domainName"`
	OriginPath           string `json:"originPath,omitempty"`
	HTTPPort             int32  `json:"httpPort,omitempty"`
	HTTPSPort            int32  `json:"httpsPort,omitempty"`
	OriginProtocolPolicy string `json:"originProtocolPolicy,omitempty"`
	Weight               int32  `json:"weight,omitempty"`
	Priority             int32  `json:"priority,omitempty"`
}
