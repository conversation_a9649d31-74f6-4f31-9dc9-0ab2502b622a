{"metadata": {"name": "test-scheduled-route", "labels": {"env": "test", "team": "cdn"}}, "spec": {"name": "测试定时路由", "description": "测试用的定时路由切换策略", "type": "Scheduled", "enabled": true, "priority": 200, "schedule": {"cronExpression": "0 */5 * * * *", "timezone": "Asia/Shanghai", "repeat": true}, "rules": [{"id": "scheduled-rule-1", "condition": {"type": "Time", "operator": "EQUALS", "value": "trigger"}, "action": {"type": "Route", "target": {"kind": "Distribution", "name": "main-cdn", "namespace": "default"}, "parameters": {"reason": "scheduled_switch"}}, "weight": 100, "priority": 1}]}}