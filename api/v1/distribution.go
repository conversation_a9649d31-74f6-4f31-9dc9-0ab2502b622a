package v1

import "time"

// DistributionSpec 定义 CDN Distribution 的规格
type DistributionSpec struct {
	// Domain 分发域名
	Domain string `json:"domain" yaml:"domain"`
	// Origins 源站配置
	Origins []OriginConfig `json:"origins" yaml:"origins"`
	// CacheConfig 缓存配置
	CacheConfig *CacheConfig `json:"cacheConfig,omitempty" yaml:"cacheConfig,omitempty"`
	// SSLConfig SSL 配置
	SSLConfig *SSLConfig `json:"sslConfig,omitempty" yaml:"sslConfig,omitempty"`
	// Vendor 云厂商
	Vendor VendorType `json:"vendor" yaml:"vendor"`
	// VendorConfig 厂商特定配置
	VendorConfig map[string]any `json:"vendorConfig,omitempty" yaml:"vendorConfig,omitempty"`
	// Enabled 是否启用
	Enabled bool `json:"enabled" yaml:"enabled"`
	// Comment 备注
	Comment string `json:"comment,omitempty" yaml:"comment,omitempty"`
}

// OriginConfig 定义源站配置
type OriginConfig struct {
	// ID 源站 ID
	ID string `json:"id" yaml:"id"`

	// DomainName 源站域名
	DomainName string `json:"domainName" yaml:"domainName"`

	// OriginPath 源站路径
	OriginPath string `json:"originPath,omitempty" yaml:"originPath,omitempty"`

	// HTTPPort HTTP 端口
	HTTPPort int32 `json:"httpPort,omitempty" yaml:"httpPort,omitempty"`

	// HTTPSPort HTTPS 端口
	HTTPSPort int32 `json:"httpsPort,omitempty" yaml:"httpsPort,omitempty"`

	// OriginProtocolPolicy 源站协议策略
	OriginProtocolPolicy OriginProtocolPolicy `json:"originProtocolPolicy,omitempty" yaml:"originProtocolPolicy,omitempty"`

	// Weight 权重
	Weight int32 `json:"weight,omitempty" yaml:"weight,omitempty"`

	// Priority 优先级
	Priority int32 `json:"priority,omitempty" yaml:"priority,omitempty"`
}

// OriginProtocolPolicy 定义源站协议策略
type OriginProtocolPolicy string

const (
	// OriginProtocolHTTPOnly 仅 HTTP
	OriginProtocolHTTPOnly OriginProtocolPolicy = "http-only"
	// OriginProtocolHTTPSOnly 仅 HTTPS
	OriginProtocolHTTPSOnly OriginProtocolPolicy = "https-only"
	// OriginProtocolMatchViewer 跟随用户
	OriginProtocolMatchViewer OriginProtocolPolicy = "match-viewer"
)

// CacheConfig 定义缓存配置
type CacheConfig struct {
	// DefaultTTL 默认 TTL (秒)
	DefaultTTL int64 `json:"defaultTTL,omitempty" yaml:"defaultTTL,omitempty"`
	// MaxTTL 最大 TTL (秒)
	MaxTTL int64 `json:"maxTTL,omitempty" yaml:"maxTTL,omitempty"`
	// MinTTL 最小 TTL (秒)
	MinTTL int64 `json:"minTTL,omitempty" yaml:"minTTL,omitempty"`
	// CacheBehaviors 缓存行为规则
	CacheBehaviors []CacheBehavior `json:"cacheBehaviors,omitempty" yaml:"cacheBehaviors,omitempty"`
	// QueryStringCaching 查询字符串缓存策略
	QueryStringCaching QueryStringCachingPolicy `json:"queryStringCaching,omitempty" yaml:"queryStringCaching,omitempty"`
	// ForwardedValues 转发值配置
	ForwardedValues *ForwardedValues `json:"forwardedValues,omitempty" yaml:"forwardedValues,omitempty"`
}

// CacheBehavior 定义缓存行为
type CacheBehavior struct {
	// PathPattern 路径模式
	PathPattern string `json:"pathPattern" yaml:"pathPattern"`
	// TTL 缓存时间 (秒)
	TTL int64 `json:"ttl" yaml:"ttl"`
	// ViewerProtocolPolicy 查看器协议策略
	ViewerProtocolPolicy ViewerProtocolPolicy `json:"viewerProtocolPolicy,omitempty" yaml:"viewerProtocolPolicy,omitempty"`
	// AllowedMethods 允许的 HTTP 方法
	AllowedMethods []string `json:"allowedMethods,omitempty" yaml:"allowedMethods,omitempty"`
	// CachedMethods 缓存的 HTTP 方法
	CachedMethods []string `json:"cachedMethods,omitempty" yaml:"cachedMethods,omitempty"`
}

// ViewerProtocolPolicy 定义查看器协议策略
type ViewerProtocolPolicy string

const (
	// ViewerProtocolAllowAll 允许所有协议
	ViewerProtocolAllowAll ViewerProtocolPolicy = "allow-all"
	// ViewerProtocolHTTPSOnly 仅 HTTPS
	ViewerProtocolHTTPSOnly ViewerProtocolPolicy = "https-only"
	// ViewerProtocolRedirectToHTTPS 重定向到 HTTPS
	ViewerProtocolRedirectToHTTPS ViewerProtocolPolicy = "redirect-to-https"
)

// QueryStringCachingPolicy 定义查询字符串缓存策略
type QueryStringCachingPolicy string

const (
	// QueryStringCachingNone 不缓存查询字符串
	QueryStringCachingNone QueryStringCachingPolicy = "none"
	// QueryStringCachingAll 缓存所有查询字符串
	QueryStringCachingAll QueryStringCachingPolicy = "all"
	// QueryStringCachingWhitelist 仅缓存白名单中的查询字符串
	QueryStringCachingWhitelist QueryStringCachingPolicy = "whitelist"
)

// ForwardedValues 定义转发值配置
type ForwardedValues struct {
	// QueryString 是否转发查询字符串
	QueryString bool `json:"queryString" yaml:"queryString"`
	// QueryStringCacheKeys 查询字符串缓存键
	QueryStringCacheKeys []string `json:"queryStringCacheKeys,omitempty" yaml:"queryStringCacheKeys,omitempty"`
	// Headers 转发的头部
	Headers []string `json:"headers,omitempty" yaml:"headers,omitempty"`
	// Cookies 转发的 Cookie
	Cookies []string `json:"cookies,omitempty" yaml:"cookies,omitempty"`
}

// SSLConfig 定义 SSL 配置
type SSLConfig struct {
	// CertificateArn 证书 ARN
	CertificateArn string `json:"certificateArn,omitempty" yaml:"certificateArn,omitempty"`
	// SSLSupportMethod SSL 支持方法
	SSLSupportMethod SSLSupportMethod `json:"sslSupportMethod,omitempty" yaml:"sslSupportMethod,omitempty"`
	// MinimumProtocolVersion 最小协议版本
	MinimumProtocolVersion string `json:"minimumProtocolVersion,omitempty" yaml:"minimumProtocolVersion,omitempty"`
}

// SSLSupportMethod 定义 SSL 支持方法
type SSLSupportMethod string

const (
	// SSLSupportSNI SNI 方式
	SSLSupportSNI SSLSupportMethod = "sni-only"
	// SSLSupportVIP VIP 方式
	SSLSupportVIP SSLSupportMethod = "vip"
)

// DistributionStatus 定义 CDN Distribution 的状态
type DistributionStatus struct {
	// Phase 当前阶段
	Phase DistributionPhase `json:"phase" yaml:"phase"`
	// Message 状态消息
	Message string `json:"message,omitempty" yaml:"message,omitempty"`
	// LastSyncTime 最后同步时间
	LastSyncTime *time.Time `json:"lastSyncTime,omitempty" yaml:"lastSyncTime,omitempty"`
	// VendorDistributionID 厂商侧分发 ID
	VendorDistributionID string `json:"vendorDistributionId,omitempty" yaml:"vendorDistributionId,omitempty"`
	// DomainName 分配的域名
	DomainName string `json:"domainName,omitempty" yaml:"domainName,omitempty"`
	// Status 厂商侧状态
	Status string `json:"status,omitempty" yaml:"status,omitempty"`
	// Conditions 状态条件
	Conditions []ResourceCondition `json:"conditions,omitempty" yaml:"conditions,omitempty"`
}

// DistributionPhase 定义 CDN Distribution 的生命周期阶段
type DistributionPhase string

const (
	DistributionPhasePending   DistributionPhase = "Pending"   // 等待创建
	DistributionPhaseCreating  DistributionPhase = "Creating"  // 创建中
	DistributionPhaseDeploying DistributionPhase = "Deploying" // 部署中
	DistributionPhaseActive    DistributionPhase = "Active"    // 活跃状态
	DistributionPhaseUpdating  DistributionPhase = "Updating"  // 更新中
	DistributionPhaseDisabled  DistributionPhase = "Disabled"  // 已禁用
	DistributionPhaseDeleting  DistributionPhase = "Deleting"  // 删除中
	DistributionPhaseFailed    DistributionPhase = "Failed"    // 失败状态
	DistributionPhaseDraft     DistributionPhase = "Draft"     // 草稿状态
)

// Distribution 定义 CDN Distribution 资源
type Distribution struct {
	// Metadata 元数据
	Metadata ResourceMetadata `json:"metadata" yaml:"metadata"`
	// Spec 规格
	Spec DistributionSpec `json:"spec" yaml:"spec"`
	// Status 状态
	Status DistributionStatus `json:"status" yaml:"status,omitempty"`
}
