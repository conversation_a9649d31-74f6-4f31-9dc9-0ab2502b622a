package dns

import (
	"encoding/json"
	"fmt"

	apiv1 "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/api/v1"
)

// DNSZoneResource 定义 DNS Zone 资源的内部表示
type DNSZoneResource struct {
	*apiv1.DNSZone
}

// NewDNSZoneResource 创建新的 DNS Zone 资源
func NewDNSZoneResource(zone *apiv1.DNSZone) *DNSZoneResource {
	return &DNSZoneResource{
		DNSZone: zone,
	}
}

// GetKey 获取资源的 etcd key
func (r *DNSZoneResource) GetKey() string {
	return "/mcdn/dns/zones/" + r.Metadata.Name
}

// GetName 获取资源名称
func (r *DNSZoneResource) GetName() string {
	return r.Metadata.Name
}

// GetNamespace 获取资源命名空间
func (r *DNSZoneResource) GetNamespace() string {
	return r.Metadata.Namespace
}

// ToJSON 转换为 JSON
func (r *DNSZoneResource) ToJSON() ([]byte, error) {
	return json.Marshal(r.DNSZone)
}

// FromJSON 从 JSON 创建资源
func (r *DNSZoneResource) FromJSON(data []byte) error {
	zone := &apiv1.DNSZone{}
	if err := json.Unmarshal(data, zone); err != nil {
		return err
	}
	r.DNSZone = zone
	return nil
}

// DNSRecordResource 定义 DNS Record 资源的内部表示
type DNSRecordResource struct {
	*apiv1.DNSRecord
}

// NewDNSRecordResource 创建新的 DNS Record 资源
func NewDNSRecordResource(record *apiv1.DNSRecord) *DNSRecordResource {
	return &DNSRecordResource{
		DNSRecord: record,
	}
}

// GetKey 获取资源的 etcd key
func (r *DNSRecordResource) GetKey() string {
	return "/mcdn/dns/records/" + r.Metadata.Name
}

// GetName 获取资源名称
func (r *DNSRecordResource) GetName() string {
	return r.Metadata.Name
}

// GetNamespace 获取资源命名空间
func (r *DNSRecordResource) GetNamespace() string {
	return r.Metadata.Namespace
}

// ToJSON 转换为 JSON
func (r *DNSRecordResource) ToJSON() ([]byte, error) {
	return json.Marshal(r.DNSRecord)
}

// FromJSON 从 JSON 创建资源
func (r *DNSRecordResource) FromJSON(data []byte) error {
	record := &apiv1.DNSRecord{}
	if err := json.Unmarshal(data, record); err != nil {
		return err
	}
	r.DNSRecord = record
	return nil
}

// DNSResourceInterface 定义 DNS 资源的通用接口
type DNSResourceInterface interface {
	GetKey() string
	GetName() string
	GetNamespace() string
	ToJSON() ([]byte, error)
	FromJSON([]byte) error
}

// ResourceType 定义资源类型
type ResourceType string

const (
	// ResourceTypeDNSZone DNS Zone 资源类型
	ResourceTypeDNSZone ResourceType = "DNSZone"
	// ResourceTypeDNSRecord DNS Record 资源类型
	ResourceTypeDNSRecord ResourceType = "DNSRecord"
)

// GetResourceType 获取 DNS Zone 资源类型
func (r *DNSZoneResource) GetResourceType() ResourceType {
	return ResourceTypeDNSZone
}

// GetResourceType 获取 DNS Record 资源类型
func (r *DNSRecordResource) GetResourceType() ResourceType {
	return ResourceTypeDNSRecord
}

// ValidateZone 验证 DNS Zone 配置
func (r *DNSZoneResource) ValidateZone() error {
	if r.Spec.Domain == "" {
		return NewValidationError("domain", "域名不能为空")
	}
	if r.Spec.Vendor == "" {
		return NewValidationError("vendor", "云厂商不能为空")
	}

	// 验证厂商类型是否支持
	switch r.Spec.Vendor {
	case apiv1.DNSVendorDnspod, apiv1.DNSVendorAliyun, apiv1.DNSVendorRoute53:
		// 支持的厂商类型
	default:
		return NewValidationError("vendor", fmt.Sprintf("不支持的DNS厂商类型: %s", r.Spec.Vendor))
	}
	return nil
}

// ValidateRecord 验证 DNS Record 配置
func (r *DNSRecordResource) ValidateRecord() error {
	if r.Spec.Zone == "" {
		return NewValidationError("zone", "所属 Zone 不能为空")
	}
	if r.Spec.Name == "" {
		return NewValidationError("name", "记录名称不能为空")
	}
	if r.Spec.Type == "" {
		return NewValidationError("type", "记录类型不能为空")
	}
	if r.Spec.Value == "" {
		return NewValidationError("value", "记录值不能为空")
	}
	return nil
}

// ValidationError 定义验证错误
type ValidationError struct {
	Field   string
	Message string
}

// NewValidationError 创建验证错误
func NewValidationError(field, message string) *ValidationError {
	return &ValidationError{
		Field:   field,
		Message: message,
	}
}

// Error 实现 error 接口
func (e *ValidationError) Error() string {
	return "validation error for field '" + e.Field + "': " + e.Message
}
