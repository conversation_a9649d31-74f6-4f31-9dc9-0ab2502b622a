package v1

import "time"

// DNSRecordSpec 定义 DNS Record 的规格
type DNSRecordSpec struct {
	// Zone 所属 Zone
	Zone string `json:"zone" yaml:"zone"`
	// Name 记录名称
	Name string `json:"name" yaml:"name"`
	// Type 记录类型 (A, AAAA, CNAME, MX, TXT, etc.)
	Type string `json:"type" yaml:"type"`
	// Value 记录值
	Value string `json:"value" yaml:"value"`
	// TTL 生存时间
	TTL int32 `json:"ttl,omitempty" yaml:"ttl,omitempty"`
	// Priority 优先级 (用于 MX 记录)
	Priority int32 `json:"priority,omitempty" yaml:"priority,omitempty"`
	// Weight 权重 (用于负载均衡)
	Weight int32 `json:"weight,omitempty" yaml:"weight,omitempty"`
}

// DNSRecordStatus 定义 DNS Record 的状态
type DNSRecordStatus struct {
	// Phase 当前阶段
	Phase DNSRecordPhase `json:"phase,omitempty" yaml:"phase,omitempty"`
	// Message 状态消息
	Message string `json:"message,omitempty" yaml:"message,omitempty"`
	// LastSyncTime 最后同步时间
	LastSyncTime *time.Time `json:"lastSyncTime,omitempty" yaml:"lastSyncTime,omitempty"`
	// VendorRecordID 厂商侧记录 ID
	VendorRecordID string `json:"vendorRecordId,omitempty" yaml:"vendorRecordId,omitempty"`
}

// DNSRecordPhase 定义 DNS Record 的生命周期阶段
type DNSRecordPhase string

const (
	DNSRecordPhasePending  DNSRecordPhase = "Pending"  // 等待创建
	DNSRecordPhaseCreating DNSRecordPhase = "Creating" // 创建中
	DNSRecordPhaseActive   DNSRecordPhase = "Active"   // 活跃状态
	DNSRecordPhaseUpdating DNSRecordPhase = "Updating" // 更新中
	DNSRecordPhaseDeleting DNSRecordPhase = "Deleting" // 删除中
	DNSRecordPhaseFailed   DNSRecordPhase = "Failed"   // 失败状态
	DNSRecordPhaseDraft    DNSRecordPhase = "Draft"    // 草稿状态
)

// DNSRecord 定义 DNS Record 资源
type DNSRecord struct {
	// Metadata 元数据
	Metadata ResourceMetadata `json:"metadata" yaml:"metadata"`
	// Spec 规格
	Spec DNSRecordSpec `json:"spec" yaml:"spec"`
	// Status 状态
	Status DNSRecordStatus `json:"status" yaml:"status,omitempty"`
}
