package distribution

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	clientv3 "go.etcd.io/etcd/client/v3"

	apiv1 "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/api/v1"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/logger"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/vendor"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/watch"
)

// Controller CDN Distribution 控制器
type Controller struct {
	name          string
	resourceType  string
	log           logger.Logger
	etcdClient    *clientv3.Client
	vendorManager vendor.Manager
	watcher       watch.Watcher
	stopCh        chan struct{}
}

// NewController 创建 CDN Distribution 控制器
func NewController(etcdClient *clientv3.Client, log logger.Logger, vendorManager vendor.Manager) *Controller {
	return &Controller{
		name:          "distribution-controller",
		resourceType:  "distribution",
		log:           log,
		etcdClient:    etcdClient,
		vendorManager: vendorManager,
		watcher:       watch.NewWatcher(etcdClient, "/mcdn/distribution/", log),
		stopCh:        make(chan struct{}),
	}
}

// Start 启动控制器
func (c *Controller) Start(ctx context.Context) error {
	// 启动 watcher
	eventChan, err := c.watcher.Start(ctx)
	if err != nil {
		return fmt.Errorf("启动 watcher 失败: %w", err)
	}

	// 启动事件处理循环
	go c.run(ctx, eventChan)
	return nil
}

// Stop 停止控制器
func (c *Controller) Stop() error {
	close(c.stopCh)
	return c.watcher.Stop()
}

// GetName 返回控制器名称
func (c *Controller) GetName() string {
	return c.name
}

// GetResourceType 返回控制器管理的资源类型
func (c *Controller) GetResourceType() string {
	return c.resourceType
}

// run 运行事件处理循环
func (c *Controller) run(ctx context.Context, eventChan <-chan *watch.Event) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-c.stopCh:
			return
		case event := <-eventChan:
			if event == nil {
				continue
			}
			if err := c.handleEvent(ctx, event); err != nil {
				c.logError("handleEvent", err, "key", event.Key)
			}
		}
	}
}

// handleEvent 处理资源事件
func (c *Controller) handleEvent(ctx context.Context, event *watch.Event) error {
	c.logInfo("handleEvent",
		"type", string(event.Type),
		"key", event.Key)

	// 处理删除事件
	if event.Type == watch.EventDeleted {
		return nil // 资源已被删除，无需处理
	}

	// 处理资源
	return c.handleDistribution(ctx, event.Key, event.Value)
}

// logError 统一的错误日志记录
func (c *Controller) logError(operation string, err error, fields ...any) {
	args := append([]any{"operation", operation, "error", err}, fields...)
	c.log.Error("操作失败", args...)
}

// logInfo 统一的信息日志记录
func (c *Controller) logInfo(operation string, fields ...any) {
	args := append([]any{"operation", operation}, fields...)
	c.log.Info("执行操作", args...)
}

// logWarn 统一的警告日志记录
func (c *Controller) logWarn(operation string, msg string, fields ...any) {
	args := append([]any{"operation", operation, "message", msg}, fields...)
	c.log.Warn("警告", args...)
}

// handleDistribution 处理 CDN Distribution 资源
func (c *Controller) handleDistribution(ctx context.Context, key string, data []byte) error {
	c.logInfo("handleDistribution", "key", key)

	// 解析 Distribution
	distribution := &apiv1.Distribution{}
	if err := json.Unmarshal(data, distribution); err != nil {
		return fmt.Errorf("解析 CDN Distribution 失败: %w", err)
	}

	// 验证配置
	if err := c.validateDistribution(distribution); err != nil {
		return c.updateDistributionStatus(ctx, distribution, apiv1.DistributionPhaseFailed, err.Error())
	}

	// 获取云厂商客户端
	vendorClient, err := c.vendorManager.GetCDNClient(distribution.Spec.Vendor)
	if err != nil {
		return c.updateDistributionStatus(ctx, distribution, apiv1.DistributionPhaseFailed,
			fmt.Sprintf("获取CDN厂商客户端失败: %v", err))
	}

	// 根据当前状态执行相应操作
	switch distribution.Status.Phase {
	case "", apiv1.DistributionPhasePending:
		return c.createDistribution(ctx, distribution, vendorClient)
	case apiv1.DistributionPhaseCreating, apiv1.DistributionPhaseDeploying:
		return c.checkDistributionStatus(ctx, distribution, vendorClient)
	case apiv1.DistributionPhaseActive:
		return c.updateDistribution(ctx, distribution, vendorClient)
	case apiv1.DistributionPhaseDeleting:
		return c.deleteDistribution(ctx, distribution, vendorClient)
	default:
		c.logWarn("handleDistribution", "未知的 CDN Distribution 状态",
			"phase", string(distribution.Status.Phase))
		return nil
	}
}

// validateDistribution 验证 Distribution 配置
func (c *Controller) validateDistribution(distribution *apiv1.Distribution) error {
	if distribution.Spec.Domain == "" {
		return fmt.Errorf("域名不能为空")
	}
	if len(distribution.Spec.Origins) == 0 {
		return fmt.Errorf("源站配置不能为空")
	}
	if distribution.Spec.Vendor == "" {
		return fmt.Errorf("云厂商不能为空")
	}

	// 验证厂商类型是否支持
	switch distribution.Spec.Vendor {
	case apiv1.CDNVendorAliyun, apiv1.CDNVendorAWS, apiv1.CDNVendorGCP, apiv1.CDNVendorAkamai:
		// 支持的厂商类型
	default:
		return fmt.Errorf("不支持的CDN厂商类型: %s", distribution.Spec.Vendor)
	}
	return nil
}

// createDistribution 创建 CDN Distribution
func (c *Controller) createDistribution(ctx context.Context, distribution *apiv1.Distribution, client vendor.CDNClient) error {
	c.logInfo("createDistribution", "domain", distribution.Spec.Domain)

	// 更新状态为创建中
	if err := c.updateDistributionStatus(ctx, distribution, apiv1.DistributionPhaseCreating, "正在创建 CDN Distribution"); err != nil {
		return err
	}

	// 构建创建请求
	request := &vendor.DistributionRequest{
		Domain:  distribution.Spec.Domain,
		Enabled: distribution.Spec.Enabled,
		Comment: distribution.Spec.Comment,
	}

	// 转换源站配置
	for _, origin := range distribution.Spec.Origins {
		request.Origins = append(request.Origins, vendor.OriginRequest{
			ID:                   origin.ID,
			DomainName:           origin.DomainName,
			OriginPath:           origin.OriginPath,
			HTTPPort:             origin.HTTPPort,
			HTTPSPort:            origin.HTTPSPort,
			OriginProtocolPolicy: string(origin.OriginProtocolPolicy),
			Weight:               origin.Weight,
			Priority:             origin.Priority,
		})
	}

	// 调用云厂商 API 创建 Distribution
	distributionID, err := client.CreateDistribution(ctx, request)
	if err != nil {
		return c.updateDistributionStatus(ctx, distribution, apiv1.DistributionPhaseFailed,
			fmt.Sprintf("创建 CDN Distribution 失败: %v", err))
	}

	// 更新状态
	distribution.Status.VendorDistributionID = distributionID
	return c.updateDistributionStatus(ctx, distribution, apiv1.DistributionPhaseDeploying, "CDN Distribution 创建成功，正在部署")
}

// updateDistributionStatus 更新 CDN Distribution 状态
func (c *Controller) updateDistributionStatus(
	ctx context.Context,
	distribution *apiv1.Distribution,
	phase apiv1.DistributionPhase,
	message string,
) error {
	now := time.Now()
	distribution.Status.Phase = phase
	distribution.Status.Message = message
	distribution.Status.LastSyncTime = &now

	// 序列化并保存到 etcd
	data, err := json.Marshal(distribution)
	if err != nil {
		c.logError("updateDistributionStatus", err, "name", distribution.Metadata.Name)
		return fmt.Errorf("序列化 CDN Distribution 失败: %w", err)
	}

	// 保存到 etcd
	key := fmt.Sprintf("/mcdn/distribution/%s", distribution.Metadata.Name)
	if _, err = c.etcdClient.Put(ctx, key, string(data)); err != nil {
		c.logError("updateDistributionStatus", err, "name", distribution.Metadata.Name)
		return fmt.Errorf("保存到 etcd 失败: %w", err)
	}

	c.logInfo("updateDistributionStatus",
		"name", distribution.Metadata.Name,
		"phase", string(phase),
		"message", message)

	return nil
}

// checkDistributionStatus 检查 CDN Distribution 状态
func (c *Controller) checkDistributionStatus(ctx context.Context, distribution *apiv1.Distribution, client vendor.CDNClient) error {
	c.logInfo("checkDistributionStatus", "name", distribution.Metadata.Name)

	// 获取分发状态
	info, err := client.GetDistribution(ctx, distribution.Status.VendorDistributionID)
	if err != nil {
		return c.updateDistributionStatus(ctx, distribution, apiv1.DistributionPhaseFailed,
			fmt.Sprintf("检查 CDN Distribution 状态失败: %v", err))
	}

	// 根据状态更新
	if info.Status == "ACTIVE" {
		return c.updateDistributionStatus(ctx, distribution, apiv1.DistributionPhaseActive,
			"CDN Distribution 已部署完成")
	}

	// 继续等待部署完成
	return c.updateDistributionStatus(ctx, distribution, apiv1.DistributionPhaseDeploying,
		fmt.Sprintf("CDN Distribution 正在部署，当前状态: %s", info.Status))
}

// updateDistribution 更新 CDN Distribution
func (c *Controller) updateDistribution(ctx context.Context, distribution *apiv1.Distribution, client vendor.CDNClient) error {
	c.logInfo("updateDistribution", "name", distribution.Metadata.Name)

	// 构建更新请求
	request := &vendor.DistributionRequest{
		Domain:  distribution.Spec.Domain,
		Enabled: distribution.Spec.Enabled,
		Comment: distribution.Spec.Comment,
	}

	// 转换源站配置
	for _, origin := range distribution.Spec.Origins {
		request.Origins = append(request.Origins, vendor.OriginRequest{
			ID:                   origin.ID,
			DomainName:           origin.DomainName,
			OriginPath:           origin.OriginPath,
			HTTPPort:             origin.HTTPPort,
			HTTPSPort:            origin.HTTPSPort,
			OriginProtocolPolicy: string(origin.OriginProtocolPolicy),
			Weight:               origin.Weight,
			Priority:             origin.Priority,
		})
	}

	// 调用云厂商 API 更新 Distribution
	if err := client.UpdateDistribution(ctx, distribution.Status.VendorDistributionID, request); err != nil {
		return c.updateDistributionStatus(ctx, distribution, apiv1.DistributionPhaseFailed,
			fmt.Sprintf("更新 CDN Distribution 失败: %v", err))
	}

	return c.updateDistributionStatus(ctx, distribution, apiv1.DistributionPhaseDeploying,
		"CDN Distribution 更新成功，正在重新部署")
}

// deleteDistribution 删除 CDN Distribution
func (c *Controller) deleteDistribution(ctx context.Context, distribution *apiv1.Distribution, client vendor.CDNClient) error {
	c.logInfo("deleteDistribution", "name", distribution.Metadata.Name)

	// 调用云厂商 API 删除 Distribution
	if err := client.DeleteDistribution(ctx, distribution.Status.VendorDistributionID); err != nil {
		return c.updateDistributionStatus(ctx, distribution, apiv1.DistributionPhaseFailed,
			fmt.Sprintf("删除 CDN Distribution 失败: %v", err))
	}

	// 从 etcd 中删除资源
	key := fmt.Sprintf("/mcdn/distribution/%s", distribution.Metadata.Name)
	if _, err := c.etcdClient.Delete(ctx, key); err != nil {
		c.logError("deleteDistribution", err, "name", distribution.Metadata.Name)
		return fmt.Errorf("从 etcd 中删除资源失败: %w", err)
	}

	return nil
}
