# 构建配置说明

## Task 构建系统

项目使用 [Task](https://taskfile.dev/) 作为构建工具，配置文件为 `Taskfile.yml`。

## 构建变量

```yaml
vars:
  APP_NAME: mcdn                  # 应用名称
  BUILD_DIR: bin                  # 构建输出目录
  CMD_DIR: ./cmd/mcdn             # 主程序目录
  VERSION: git describe --tags    # 版本号（Git 标签）
  COMMIT: git rev-parse --short   # Git 提交哈希
  BUILD_TIME: date -u             # 构建时间（UTC）
  GOFLAGS: -trimpath              # Go 编译标志
  LDFLAGS: -ldflags "-s -w"       # 链接器标志
  EXE_NAME: mcdn                  # 可执行文件名
```

## 环境变量

```yaml
env:
  CGO_ENABLED: 0                  # 禁用 CGO，生成静态二进制文件
```

## 可用任务

### 开发任务

#### `task dev`

开发模式运行，不构建二进制文件：

```bash
go run ./cmd/mcdn run -c config.yaml
```

#### `task run`

构建并运行：

```bash
task build
./bin/mcdn run -c config.yaml
```

#### `task format`

代码格式化：

```bash
goimports -w .
gofumpt -l -w .
```

#### `task lint`

代码检查：

```bash
golangci-lint run --timeout=3m ./...
```

#### `task test`

运行测试：

```bash
go test -v ./...
```

### 构建任务

#### `task build`

标准构建，生成优化的二进制文件：

```bash
mkdir -p bin
go build -trimpath -gcflags "-N -l" \
  -ldflags "-s -w -extldflags '-static' \
  -X 'gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/pkg/version.Version=v1.0.0'" \
  -o bin/mcdn \
  ./cmd/mcdn/
```

**构建优化参数说明：**

- `-trimpath`: 移除文件系统路径信息
- `-gcflags "-N -l"`: 禁用优化和内联（调试用）
- `-ldflags "-s -w"`: 去除符号表和调试信息
- `-extldflags '-static'`: 静态链接
- `-X`: 注入版本信息

#### `task build-compress`

构建并使用 UPX 压缩：

```bash
task build
upx --best --lzma bin/mcdn
```

### 工具任务

#### `task install-tools`

安装开发工具：

```bash
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
go install golang.org/x/tools/cmd/goimports@latest
go install mvdan.cc/gofumpt@latest
```

#### `task tidy`

整理 Go modules：

```bash
go mod tidy
```

#### `task clean`

清理构建产物：

```bash
rm -rf bin
go clean -cache
```

#### `task all`

完整构建流程：

```bash
task clean
task format
task lint
task test
task build
task build-compress
```

## 版本信息注入

构建时会自动注入以下版本信息：

```go
// pkg/version/version.go
var (
    Version   = "dev"           // Git 标签或 "dev"
    Commit    = "unknown"       // Git 提交哈希（短）
    BuildTime = "unknown"       # 构建时间（UTC）
)
```

注入方式：

```bash
-ldflags "-X 'gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/pkg/version.Version=${VERSION}'"
```

## 交叉编译

### Linux AMD64

```bash
GOOS=linux GOARCH=amd64 task build
```

### Linux ARM64

```bash
GOOS=linux GOARCH=arm64 task build
```

### Windows AMD64

```bash
GOOS=windows GOARCH=amd64 task build
```

## Docker 构建

### 多阶段构建 Dockerfile 示例

```dockerfile
# 构建阶段
FROM golang:1.24-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build \
    -trimpath \
    -ldflags "-s -w -extldflags '-static'" \
    -o mcdn \
    ./cmd/mcdn/

# 运行阶段
FROM scratch
COPY --from=builder /app/mcdn /mcdn
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
ENTRYPOINT ["/mcdn"]
```

## 构建优化

### 二进制文件大小优化

1. **禁用 CGO**: `CGO_ENABLED=0`
2. **去除调试信息**: `-ldflags="-s -w"`
3. **移除路径信息**: `-trimpath`
4. **UPX 压缩**: `upx --best --lzma`

### 构建速度优化

1. **并行构建**: 设置 `GOMAXPROCS`
2. **构建缓存**: 利用 Go 模块缓存
3. **增量构建**: 只构建变更的包

### 安全构建

1. **静态链接**: `-extldflags '-static'`
2. **最小化镜像**: 使用 `scratch` 基础镜像
3. **非 root 用户**: 容器中使用非特权用户

## CI/CD 集成

### GitHub Actions 示例

```yaml
name: Build and Test

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-go@v3
      with:
        go-version: '1.24'
    
    - name: Install Task
      run: |
        sh -c "$(curl --location https://taskfile.dev/install.sh)" -- -d -b /usr/local/bin
    
    - name: Run tests
      run: task all
```

### GitLab CI 示例

```yaml
stages:
  - test
  - build

test:
  stage: test
  image: golang:1.24
  script:
    - curl -sL https://taskfile.dev/install.sh | sh
    - ./bin/task all

build:
  stage: build
  image: golang:1.24
  script:
    - curl -sL https://taskfile.dev/install.sh | sh
    - ./bin/task build
  artifacts:
    paths:
      - bin/
```

## 性能基准测试

```bash
# 运行基准测试
go test -bench=. -benchmem ./...

# 生成 CPU 性能分析
go test -cpuprofile=cpu.prof -bench=. ./...

# 生成内存性能分析
go test -memprofile=mem.prof -bench=. ./...
```

## 构建最佳实践

1. **版本管理**: 使用语义化版本标签
2. **构建缓存**: 合理利用 Docker 层缓存
3. **安全扫描**: 集成漏洞扫描工具
4. **自动化**: 完全自动化的 CI/CD 流程
5. **监控**: 构建时间和产物大小监控
