package controller

import (
	"fmt"
	"reflect"

	clientv3 "go.etcd.io/etcd/client/v3"

	apiv1 "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/api/v1"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/logger"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/vendor"
)

// Resource 是 apiv1.Resource 的别名，避免重复定义
type Resource = apiv1.Resource

// ManagerInterface 定义 Manager 的接口，用于依赖注入和测试
type ManagerInterface interface {
	// RegisterController 注册控制器
	RegisterController(controller Controller) error
	// GetEtcdClient 获取 etcd 客户端
	GetEtcdClient() *clientv3.Client
	// GetLogger 获取日志记录器
	GetLogger() logger.Logger
	// GetVendorManager 获取 vendor 管理器
	GetVendorManager() vendor.Manager
}

// ControllerBuilder 控制器构建器，提供流式 API 来配置和创建控制器
type ControllerBuilder struct {
	manager      ManagerInterface
	etcdClient   *clientv3.Client
	vendorMgr    vendor.Manager
	logger       logger.Logger
	name         string
	resourceType string
	resource     Resource // 具体的资源类型
	prefix       string
}

// NewControllerManagedBy 创建由指定 Manager 管理的控制器构建器
// 类似于 K8s controller-runtime 的 ctrl.NewControllerManagedBy(mgr)
// 优化：只需要一个 manager 参数，所有依赖都从 manager 获取
func NewControllerManagedBy(mgr ManagerInterface) *ControllerBuilder {
	return &ControllerBuilder{
		manager:    mgr,
		etcdClient: mgr.GetEtcdClient(),
		logger:     mgr.GetLogger(),
		vendorMgr:  mgr.GetVendorManager(),
	}
}

// Named 设置控制器名称
func (b *ControllerBuilder) Named(name string) *ControllerBuilder {
	b.name = name
	return b
}

// For 指定控制器管理的资源类型 - 类型安全的版本
// 类似于 K8s 的 For(&v1.Pod{})，但适配我们的架构
// 支持三种使用方式：
// 1. For(&RouteResource{})     - 使用具体的资源实例（推荐）
// 2. For((*RouteResource)(nil)) - 使用 nil 指针获取类型信息
// 3. For("route")              - 使用字符串（向下兼容）
func (b *ControllerBuilder) For(obj any) *ControllerBuilder {
	switch v := obj.(type) {
	case Resource:
		// 传入了具体的资源实例
		b.resource = v
		b.resourceType = v.GetKind()
		b.prefix = v.GetEtcdPrefix()
	case string:
		// 向下兼容：传入字符串类型
		b.resourceType = v
		// 根据字符串推断 prefix（可以通过注册表进一步优化）
		b.prefix = fmt.Sprintf("/mcdn/%s/", v)
	default:
		// 尝试通过反射获取类型信息
		t := reflect.TypeOf(obj)
		if t != nil {
			// 如果是指针，获取元素类型
			if t.Kind() == reflect.Ptr {
				t = t.Elem()
			}
			// 如果实现了 Resource 接口，创建零值实例
			if t.Implements(reflect.TypeOf((*Resource)(nil)).Elem()) {
				zeroValue := reflect.New(t).Interface().(Resource)
				b.resource = zeroValue
				b.resourceType = zeroValue.GetKind()
				b.prefix = zeroValue.GetEtcdPrefix()
			} else {
				// 回退到类型名称
				b.resourceType = t.Name()
				b.prefix = fmt.Sprintf("/mcdn/%s/", b.resourceType)
			}
		}
	}
	return b
}

// WithEtcdPrefix 设置 etcd 监听前缀（可选，通常从资源类型自动推断）
func (b *ControllerBuilder) WithEtcdPrefix(prefix string) *ControllerBuilder {
	b.prefix = prefix
	return b
}

// Complete 完成控制器构建并注册到 Manager
func (b *ControllerBuilder) Complete(controller Controller) error {
	// 验证必要的参数
	if b.name == "" {
		return fmt.Errorf("控制器名称不能为空")
	}
	if b.resourceType == "" {
		return fmt.Errorf("资源类型不能为空")
	}

	// 注册到管理器
	return b.manager.RegisterController(controller)
}

// Build 构建控制器但不注册（用于手动注册的场景）
func (b *ControllerBuilder) Build() (*ControllerBuilder, error) {
	// 验证配置的完整性
	if b.etcdClient == nil {
		return nil, fmt.Errorf("etcd 客户端未设置")
	}
	if b.logger == nil {
		return nil, fmt.Errorf("日志记录器未设置")
	}

	return b, nil
}

// GetEtcdClient 获取 etcd 客户端
func (b *ControllerBuilder) GetEtcdClient() *clientv3.Client {
	return b.etcdClient
}

// GetVendorManager 获取 vendor 管理器
func (b *ControllerBuilder) GetVendorManager() vendor.Manager {
	return b.vendorMgr
}

// GetLogger 获取日志记录器
func (b *ControllerBuilder) GetLogger() logger.Logger {
	return b.logger
}

// GetName 获取控制器名称
func (b *ControllerBuilder) GetName() string {
	return b.name
}

// GetResourceType 获取资源类型
func (b *ControllerBuilder) GetResourceType() string {
	return b.resourceType
}

// GetEtcdPrefix 获取 etcd 前缀
func (b *ControllerBuilder) GetEtcdPrefix() string {
	return b.prefix
}
