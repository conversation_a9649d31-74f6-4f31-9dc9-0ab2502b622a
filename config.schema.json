{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://lilithgame.com/schemas/mcdn/config.json", "title": "MCDN Worker Configuration Schema", "description": "Configuration schema for mcdn service", "type": "object", "properties": {"env": {"type": "string", "description": "Environment (dev, prod, etc.)", "default": "dev", "enum": ["dev", "test", "staging", "prod"]}, "log": {"type": "object", "description": "Logging configuration", "properties": {"path": {"type": "string", "description": "Log file path (empty string means output to console)", "default": "", "examples": ["./log/mcdn.log", "/var/log/mcdn.log", ""]}, "level": {"type": "string", "description": "Log level", "default": "info", "enum": ["trace", "debug", "info", "warn", "error", "fatal", "panic"]}}, "required": ["level"], "additionalProperties": false}, "api": {"type": "object", "description": "API server configuration", "properties": {"enabled": {"type": "boolean", "description": "Enable API server", "default": true}, "host": {"type": "string", "description": "API server host", "default": "0.0.0.0", "examples": ["0.0.0.0", "127.0.0.1", "localhost"]}, "port": {"type": "integer", "description": "API server port", "default": 8080, "minimum": 1, "maximum": 65535}}, "required": ["enabled", "host", "port"], "additionalProperties": false}, "controller": {"type": "object", "description": "Controller configuration", "properties": {"enabled": {"type": "boolean", "description": "Enable controller", "default": true}, "workers": {"type": "integer", "description": "Number of controller workers", "default": 2, "minimum": 1, "maximum": 100}}, "required": ["enabled", "workers"], "additionalProperties": false}, "worker": {"type": "object", "description": "Worker configuration", "properties": {"concurrency": {"type": "integer", "description": "Number of concurrent workers", "default": 4, "minimum": 1, "maximum": 100}, "queue": {"type": "string", "description": "Queue name", "default": "default", "minLength": 1, "pattern": "^[a-zA-Z0-9_-]+$"}}, "required": ["concurrency", "queue"], "additionalProperties": false}, "etcd": {"type": "object", "description": "ETCD configuration", "properties": {"endpoints": {"type": "array", "description": "ETCD endpoints", "items": {"type": "string", "pattern": "^.+:[0-9]+$"}, "minItems": 1, "default": ["127.0.0.1:2379"], "examples": [["127.0.0.1:2379"], ["etcd1:2379", "etcd2:2379", "etcd3:2379"]]}, "timeout": {"type": "string", "description": "ETCD connection timeout (Go duration format)", "default": "5s", "pattern": "^[0-9]+(ns|us|µs|ms|s|m|h)$", "examples": ["5s", "10s", "1m"]}}, "required": ["endpoints", "timeout"], "additionalProperties": false}}, "required": ["log"], "additionalProperties": false}