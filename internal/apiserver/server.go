package apiserver

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	clientv3 "go.etcd.io/etcd/client/v3"

	apiv1 "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/api/v1"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/logger"
)

// Config API 服务器配置
type Config struct {
	EtcdClient *clientv3.Client
	Logger     logger.Logger
}

// Server API 服务器
type Server struct {
	etcdClient *clientv3.Client
	logger     logger.Logger
}

// New 创建新的 API 服务器
func New(config Config) *Server {
	return &Server{
		etcdClient: config.EtcdClient,
		logger:     config.Logger,
	}
}

// RegisterRoutes 注册路由
func (s *Server) RegisterRoutes(router *gin.Engine) {
	// 健康检查
	router.GET("/health", s.healthCheck)

	// API v1 路由组
	v1 := router.Group("/api/v1")
	{
		// Distribution 资源
		distributions := v1.Group("/distributions")
		{
			distributions.GET("", s.listDistributions)
			distributions.POST("", s.createDistribution)
			distributions.GET("/:name", s.getDistribution)
			distributions.PUT("/:name", s.updateDistribution)
			distributions.DELETE("/:name", s.deleteDistribution)
		}

		// DNS 资源
		dns := v1.Group("/dns")
		{
			// DNS Zones
			zones := dns.Group("/zones")
			{
				zones.GET("", s.listDNSZones)
				zones.POST("", s.createDNSZone)
				zones.GET("/:name", s.getDNSZone)
				zones.PUT("/:name", s.updateDNSZone)
				zones.DELETE("/:name", s.deleteDNSZone)
			}

			// DNS Records
			records := dns.Group("/records")
			{
				records.GET("", s.listDNSRecords)
				records.POST("", s.createDNSRecord)
				records.GET("/:name", s.getDNSRecord)
				records.PUT("/:name", s.updateDNSRecord)
				records.DELETE("/:name", s.deleteDNSRecord)
			}
		}

		// Policy 资源
		policies := v1.Group("/policies")
		{
			policies.GET("", s.listPolicies)
			policies.POST("", s.createPolicy)
			policies.GET("/:name", s.getPolicy)
			policies.PUT("/:name", s.updatePolicy)
			policies.DELETE("/:name", s.deletePolicy)
		}
	}
}

// healthCheck 健康检查
func (s *Server) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"service": "mcdn-apiserver",
	})
}

// Distribution 相关处理器
func (s *Server) listDistributions(c *gin.Context) {
	// TODO: 实现列出所有 Distribution
	c.JSON(http.StatusOK, gin.H{"message": "list distributions"})
}

func (s *Server) createDistribution(c *gin.Context) {
	// TODO: 实现创建 Distribution
	c.JSON(http.StatusCreated, gin.H{"message": "create distribution"})
}

func (s *Server) getDistribution(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现获取指定 Distribution
	c.JSON(http.StatusOK, gin.H{"message": "get distribution", "name": name})
}

func (s *Server) updateDistribution(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现更新 Distribution
	c.JSON(http.StatusOK, gin.H{"message": "update distribution", "name": name})
}

func (s *Server) deleteDistribution(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现删除 Distribution
	c.JSON(http.StatusOK, gin.H{"message": "delete distribution", "name": name})
}

// DNS Zone 相关处理器
func (s *Server) listDNSZones(c *gin.Context) {
	// TODO: 实现列出所有 DNS Zone
	c.JSON(http.StatusOK, gin.H{"message": "list dns zones"})
}

func (s *Server) createDNSZone(c *gin.Context) {
	// TODO: 实现创建 DNS Zone
	c.JSON(http.StatusCreated, gin.H{"message": "create dns zone"})
}

func (s *Server) getDNSZone(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现获取指定 DNS Zone
	c.JSON(http.StatusOK, gin.H{"message": "get dns zone", "name": name})
}

func (s *Server) updateDNSZone(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现更新 DNS Zone
	c.JSON(http.StatusOK, gin.H{"message": "update dns zone", "name": name})
}

func (s *Server) deleteDNSZone(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现删除 DNS Zone
	c.JSON(http.StatusOK, gin.H{"message": "delete dns zone", "name": name})
}

// DNS Record 相关处理器
func (s *Server) listDNSRecords(c *gin.Context) {
	// TODO: 实现列出所有 DNS Record
	c.JSON(http.StatusOK, gin.H{"message": "list dns records"})
}

func (s *Server) createDNSRecord(c *gin.Context) {
	// TODO: 实现创建 DNS Record
	c.JSON(http.StatusCreated, gin.H{"message": "create dns record"})
}

func (s *Server) getDNSRecord(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现获取指定 DNS Record
	c.JSON(http.StatusOK, gin.H{"message": "get dns record", "name": name})
}

func (s *Server) updateDNSRecord(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现更新 DNS Record
	c.JSON(http.StatusOK, gin.H{"message": "update dns record", "name": name})
}

func (s *Server) deleteDNSRecord(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现删除 DNS Record
	c.JSON(http.StatusOK, gin.H{"message": "delete dns record", "name": name})
}

// Policy 相关处理器
func (s *Server) listPolicies(c *gin.Context) {
	// TODO: 实现列出所有 Policy
	c.JSON(http.StatusOK, gin.H{"message": "list policies"})
}

func (s *Server) createPolicy(c *gin.Context) {
	s.logger.Info("收到创建 Policy 请求")

	// 解析请求体
	var policy apiv1.Policy
	if err := c.ShouldBindJSON(&policy); err != nil {
		s.logger.Error("解析请求体失败", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "invalid_request",
			"message": fmt.Sprintf("请求体格式错误: %v", err),
		})
		return
	}

	// 验证必需字段
	if policy.Metadata.Name == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "validation_failed",
			"message": "资源名称不能为空",
		})
		return
	}

	// 设置默认状态
	if policy.Status.Phase == "" {
		policy.Status.Phase = apiv1.PolicyPhasePending
	}
	now := time.Now()
	policy.Status.LastSyncTime = &now

	s.logger.Info("创建 Policy 资源",
		"name", policy.Metadata.Name,
		"type", string(policy.Spec.Type),
		"enabled", policy.Spec.Enabled)

	// 序列化为 JSON
	data, err := json.Marshal(policy)
	if err != nil {
		s.logger.Error("序列化 Policy 失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "internal_error",
			"message": "序列化资源失败",
		})
		return
	}

	// 写入 etcd
	key := fmt.Sprintf("/mcdn/policy/%s", policy.Metadata.Name)
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
	defer cancel()

	_, err = s.etcdClient.Put(ctx, key, string(data))
	if err != nil {
		s.logger.Error("写入 etcd 失败", "key", key, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "storage_error",
			"message": fmt.Sprintf("保存资源失败: %v", err),
		})
		return
	}

	s.logger.Info("Policy 资源创建成功", "name", policy.Metadata.Name, "key", key)

	c.JSON(http.StatusCreated, gin.H{
		"message": "Policy 创建成功",
		"data":    policy,
	})
}

func (s *Server) getPolicy(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现获取指定 Policy
	c.JSON(http.StatusOK, gin.H{"message": "get policy", "name": name})
}

func (s *Server) updatePolicy(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现更新 Policy
	c.JSON(http.StatusOK, gin.H{"message": "update policy", "name": name})
}

func (s *Server) deletePolicy(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现删除 Policy
	c.JSON(http.StatusOK, gin.H{"message": "delete policy", "name": name})
}
