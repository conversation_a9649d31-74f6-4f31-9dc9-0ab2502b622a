# Golang 版本和依赖配置

## Go 版本

- **当前版本**: Go 1.24.6
- **最低要求**: Go 1.21+
- **推荐版本**: Go 1.24.x (最新稳定版)

## 模块信息

```go
module gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn

go 1.24.6
```

## 核心依赖

### 配置管理

- **github.com/gin-gonic/gin v1.10.1** - HTTP 框架
- **github.com/spf13/viper v1.20.1** - 配置文件管理
- **github.com/urfave/cli/v2 v2.27.7** - 命令行接口

### 数据存储

- **go.etcd.io/etcd/api/v3 v3.6.4** - etcd API 定义
- **go.etcd.io/etcd/client/v3 v3.6.4** - etcd 客户端

### 内部依赖

- **gitlab.lilithgame.com/yunwei/pkg v0.2.7** - 内部公共包

### 配置和序列化

- **gopkg.in/yaml.v3 v3.0.1** - YAML 处理
- **github.com/pelletier/go-toml/v2 v2.2.4** - TOML 处理
- **github.com/go-viper/mapstructure/v2 v2.4.0** - 结构体映射

## 开发工具依赖

### 代码质量

- **github.com/golangci/golangci-lint** - 代码检查工具
- **golang.org/x/tools/cmd/goimports** - 导入管理
- **mvdan.cc/gofumpt** - 代码格式化

### 构建工具

- **github.com/go-task/task/v3** - 任务运行器

## 依赖管理策略

### 版本固定

- 所有直接依赖都固定到具体版本
- 定期更新依赖到最新稳定版本
- 重要安全更新及时应用

### 依赖更新流程

```bash
# 检查可更新的依赖
go list -u -m all

# 更新所有依赖到最新版本
go get -u ./...

# 更新特定依赖
go get -u github.com/spf13/viper

# 清理未使用的依赖
go mod tidy
```

### 安全扫描

```bash
# 检查已知漏洞
go list -json -deps ./... | nancy sleuth

# 或使用 govulncheck
govulncheck ./...
```

## 构建配置

### CGO 设置

```bash
# 禁用 CGO 以生成静态二进制文件
export CGO_ENABLED=0
```

### 构建标签

- 支持不同环境的构建标签
- 开发环境: `dev`
- 生产环境: `prod`

### 交叉编译支持

```bash
# Linux AMD64
GOOS=linux GOARCH=amd64 go build

# Linux ARM64
GOOS=linux GOARCH=arm64 go build

# Windows AMD64
GOOS=windows GOARCH=amd64 go build
```

## 性能优化

### 编译优化

- 使用 `-ldflags="-s -w"` 减小二进制文件大小
- 使用 `-trimpath` 移除文件系统路径
- 可选择使用 UPX 进一步压缩

### 运行时优化

- 合理设置 `GOMAXPROCS`
- 调整 GC 参数 `GOGC`
- 监控内存使用情况

## 兼容性

### Go 版本兼容性

- 向后兼容 Go 1.21+
- 利用 Go 1.24 的新特性
- 定期测试新版本兼容性

### 平台兼容性

- Linux (主要目标平台)
- macOS (开发环境)

## 最佳实践

### 模块管理

1. 使用语义化版本
2. 定期更新依赖
3. 避免循环依赖
4. 最小化依赖数量

### 代码组织

1. 遵循 Go 项目布局标准
2. 使用内部包隐藏实现细节
3. 清晰的包职责划分
4. 合理的接口设计

### 错误处理

1. 使用 `errors.Is` 和 `errors.As`
2. 包装错误提供上下文
3. 定义项目特定的错误类型
4. 避免忽略错误
