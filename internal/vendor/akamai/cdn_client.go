package akamai

import (
	"context"
	"fmt"

	apiv1 "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/api/v1"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/vendor"
)

// CDNClient Akamai CDN 客户端
type CDNClient struct {
	accessToken  string
	clientToken  string
	clientSecret string
	host         string
}

// NewCDNClient 创建 Akamai CDN 客户端
func NewCDNClient(accessToken, clientToken, clientSecret, host string) *CDNClient {
	return &CDNClient{
		accessToken:  accessToken,
		clientToken:  clientToken,
		clientSecret: clientSecret,
		host:         host,
	}
}

// GetCDNVendorType 返回CDN厂商类型
func (c *CDNClient) GetCDNVendorType() apiv1.VendorType {
	return apiv1.CDNVendorAkamai
}

// ValidateConfig 验证配置
func (c *CDNClient) ValidateConfig(config map[string]any) error {
	if c.accessToken == "" {
		return fmt.Errorf("AccessToken 不能为空")
	}
	if c.clientToken == "" {
		return fmt.Errorf("ClientToken 不能为空")
	}
	if c.clientSecret == "" {
		return fmt.Errorf("ClientSecret 不能为空")
	}
	if c.host == "" {
		return fmt.Errorf("Host 不能为空")
	}
	return nil
}

// CreateDistribution 创建 CDN Distribution
func (c *CDNClient) CreateDistribution(ctx context.Context, config *vendor.DistributionRequest) (string, error) {
	// TODO: 实现 Akamai CDN Distribution 创建逻辑
	// 这里应该调用 Akamai API
	return fmt.Sprintf("akamai-dist-%s", config.Domain), nil
}

// UpdateDistribution 更新 CDN Distribution
func (c *CDNClient) UpdateDistribution(ctx context.Context, distributionID string, config *vendor.DistributionRequest) error {
	// TODO: 实现 Akamai CDN Distribution 更新逻辑
	return nil
}

// DeleteDistribution 删除 CDN Distribution
func (c *CDNClient) DeleteDistribution(ctx context.Context, distributionID string) error {
	// TODO: 实现 Akamai CDN Distribution 删除逻辑
	return nil
}

// GetDistribution 获取 CDN Distribution
func (c *CDNClient) GetDistribution(ctx context.Context, distributionID string) (*vendor.DistributionInfo, error) {
	// TODO: 实现 Akamai CDN Distribution 查询逻辑
	return &vendor.DistributionInfo{
		ID:     distributionID,
		Status: "Active",
	}, nil
}

// ListDistributions 列出 CDN Distributions
func (c *CDNClient) ListDistributions(ctx context.Context) ([]*vendor.DistributionInfo, error) {
	// TODO: 实现 Akamai CDN Distribution 列表逻辑
	return []*vendor.DistributionInfo{}, nil
}
