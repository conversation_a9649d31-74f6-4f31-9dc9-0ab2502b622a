# MCDN Worker Copilot 指令

## 项目描述

MCDN Worker 是一个多云CDN调度管理平台的 Worker 模块，使用 Go 语言开发。它负责执行来自 MCDN 调度系统的各种任务。

## 文档导航

本项目的完整文档已模块化组织，请根据需要查看相应文档：

- **[架构设计](./architecture.md)** - 整体架构、设计模式和核心组件
- **[开发指南](./development-guide.md)** - 环境设置、构建运行和测试规范
- **[代码规范](./coding-standards.md)** - 代码风格、命名规范和最佳实践
- **[API 设计](./api-design.md)** - API 设计规范、资源定义和使用示例
- **[故障排查](./troubleshooting.md)** - 调试方法、性能监控和问题解决

## 目录结构

```bash
mcdn/
├── README.md
├── go.mod
├── go.sum
├── config.example.yaml
├── config.schema.json
├── .gitignore
├── .golangci.yml
├── Taskfile.yml
├── TASKFILE.md
├── .vscode/
├── cmd/
│   └── mcdn/
│       └── main.go
├── api/
│   └── v1/
├── internal/
│   ├── controller/
│   ├── resource/
│   └── vendor/
├── pkg/
│   ├── app/
│   ├── config/
│   ├── version/
│   └── worker/
└── logs/
```

## 项目功能

Worker 模块负责接收和执行来自 MCDN 调度系统的任务，包括但不限于：

- CDN 配置更新
- 缓存刷新
- 域名调度
- 性能监控
- 故障处理

## 快速开始

### 环境要求

- Go 1.19+
- etcd 3.5+
- Task CLI tool

### 基础命令

```bash
# 查看所有可用任务
task --list

# 安装依赖
task tidy

# 构建项目
task build

# 运行项目
task run

# 开发模式
task dev

# 运行测试
task test

# 代码检查
task lint

# 格式化代码
task format
```

## 核心特性

- **事件驱动架构** - 基于 etcd 的事件监听和处理
- **多云厂商支持** - 统一接口支持 AWS、阿里云、Akamai 等
- **Kubernetes 风格** - 遵循云原生设计理念和最佳实践
- **高可用设计** - 支持集群部署和故障恢复
- **可观测性** - 完整的日志、监控和调试支持

## 贡献指南

如果您想为项目做贡献，请：

1. 阅读相关文档了解项目架构和规范
2. 遵循 [代码规范](./coding-standards.md) 进行开发
3. 按照 [开发指南](./development-guide.md) 进行测试
4. 遵循 Conventional Commits 规范提交代码

## 支持

- 查看 [故障排查指南](./troubleshooting.md) 解决常见问题
- 参考 [API 设计文档](./api-design.md) 了解接口使用
- 阅读 [架构文档](./architecture.md) 理解系统设计
