linters:
    enable:
        - gofumpt
        - gosimple
        - gofmt
        - unused
        - errname
        - gci
        - gofmt
        - lll
        - importas  # 用于规范 import 别名
        - intrange  # 使用整数范围的 for 循环
        - exptostd  # 检测可以被标准库函数替换的 golang.org/x/exp/ 包中的函数 (包括 slices.Contains 等现代化建议)

linters-settings:
    gosimple:
        checks: ["all"]
    gofmt: # 代码格式化设置
        simplify: false
        rewrite-rules:
            - pattern: "interface{}"
              replacement: "any"
            - pattern: "a[b:len(a)]"
              replacement: "a[b:]"
    gci:
        sections:
            - standard
            - default
            - prefix(gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn)

    lll:
      line-length: 150

    importas:  # 规范 import 别名
        alias:
            - pkg: go.etcd.io/etcd/client/v3
              alias: clientv3
            - pkg: gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/api/v1
              alias: apiv1
            - pkg: github.com/urfave/cli/v2
              alias: cliv2

    usestdlibvars:
        http-method: true
        http-status-code: true
        time-layout: true
        time-weekday: true
        time-month: true


issues:
    fix: true
