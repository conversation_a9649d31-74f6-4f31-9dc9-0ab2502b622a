package v1

import (
	"slices"
	"time"
)

// ResourceMetadata 定义资源的通用元数据
type ResourceMetadata struct {
	// Name 资源名称
	Name string `json:"name" yaml:"name"`
	// Namespace 命名空间
	Namespace string `json:"namespace,omitempty" yaml:"namespace,omitempty"`
	// Labels 标签，用于资源分类和选择
	Labels map[string]string `json:"labels,omitempty" yaml:"labels,omitempty"`

	// Annotations 注解，用于存储非识别性元数据，支持临时标记和导入场景
	// 常用注解：
	// - "mcdn.lilithgames.com/import-source": 标记导入来源
	// - "mcdn.lilithgames.com/import-time": 标记导入时间
	// - "mcdn.lilithgames.com/original-id": 保存原始系统中的ID
	// - "mcdn.lilithgames.com/migration-notes": 迁移备注信息
	Annotations map[string]string `json:"annotations,omitempty" yaml:"annotations,omitempty"`

	// CreationTimestamp 创建时间
	CreationTimestamp *time.Time `json:"creationTimestamp,omitempty" yaml:"creationTimestamp,omitempty"`

	// DeletionTimestamp 删除时间戳，设置后资源将被删除
	DeletionTimestamp *time.Time `json:"deletionTimestamp,omitempty" yaml:"deletionTimestamp,omitempty"`

	// Finalizers 终结器列表，用于资源清理
	Finalizers []string `json:"finalizers,omitempty" yaml:"finalizers,omitempty"`
}

// ResourceCondition 定义资源状态条件
type ResourceCondition struct {
	// Type 条件类型
	Type string `json:"type" yaml:"type"`
	// Status 条件状态 (True, False, Unknown)
	Status ConditionStatus `json:"status" yaml:"status"`
	// Reason 原因
	Reason string `json:"reason,omitempty" yaml:"reason,omitempty"`
	// Message 详细消息
	Message string `json:"message,omitempty" yaml:"message,omitempty"`
	// LastTransitionTime 最后转换时间
	LastTransitionTime *time.Time `json:"lastTransitionTime,omitempty" yaml:"lastTransitionTime,omitempty"`
	// LastUpdateTime 最后更新时间
	LastUpdateTime *time.Time `json:"lastUpdateTime,omitempty" yaml:"lastUpdateTime,omitempty"`
}

// ConditionStatus 定义条件状态
type ConditionStatus string

const (
	// ConditionTrue 条件为真
	ConditionTrue ConditionStatus = "True"
	// ConditionFalse 条件为假
	ConditionFalse ConditionStatus = "False"
	// ConditionUnknown 条件未知
	ConditionUnknown ConditionStatus = "Unknown"
)

// 常用注解键
const (
	// AnnotationPrefix 注解前缀，使用域名格式
	AnnotationPrefix = "mcdn.lilithgames.com"

	// 迁移相关注解 - 符合 Kubernetes 云原生风格
	AnnotationImportSource   = AnnotationPrefix + "/import-source"   // 导入来源注解键
	AnnotationImportTime     = AnnotationPrefix + "/import-time"     // 导入时间注解键
	AnnotationOriginalID     = AnnotationPrefix + "/original-id"     // 原始ID注解键
	AnnotationMigrationNotes = AnnotationPrefix + "/migration-notes" // 迁移备注注解键
)

// VendorType 定义支持的厂商类型
type VendorType string

// DNS 提供商
const (
	DNSVendorDnspod  VendorType = "dnspod"     // 腾讯云 DNSPod
	DNSVendorAliyun  VendorType = "aliyun_dns" // 阿里云 DNS
	DNSVendorRoute53 VendorType = "route53"    // AWS Route53
)

// CDN 提供商
const (
	CDNVendorAWS    VendorType = "aws_cdn"
	CDNVendorGCP    VendorType = "gcp_media" // GCP Media CDN
	CDNVendorAliyun VendorType = "aliyun_cdn"
	CDNVendorAkamai VendorType = "akamai"
)

// ResourceReference 定义资源引用
type ResourceReference struct {
	// Kind 资源类型
	Kind string `json:"kind" yaml:"kind"`
	// Name 资源名称
	Name string `json:"name" yaml:"name"`
	// Namespace 命名空间
	Namespace string `json:"namespace,omitempty" yaml:"namespace,omitempty"`
}

// VendorConfig 定义厂商配置
type VendorConfig struct {
	// Type 厂商类型
	Type VendorType `json:"type" yaml:"type"`
	// Region 区域
	Region string `json:"region,omitempty" yaml:"region,omitempty"`
	// AccessKeyID 访问密钥 ID
	AccessKeyID string `json:"accessKeyId,omitempty" yaml:"accessKeyId,omitempty"`
	// SecretAccessKey 访问密钥
	SecretAccessKey string `json:"secretAccessKey,omitempty" yaml:"secretAccessKey,omitempty"`
	// Endpoint 自定义端点
	Endpoint string `json:"endpoint,omitempty" yaml:"endpoint,omitempty"`
	// Extra 额外配置
	Extra map[string]any `json:"extra,omitempty" yaml:"extra,omitempty"`
}

// ListOptions 定义列表查询选项
type ListOptions struct {
	// LabelSelector 标签选择器
	LabelSelector string `json:"labelSelector,omitempty" yaml:"labelSelector,omitempty"`

	// FieldSelector 字段选择器
	FieldSelector string `json:"fieldSelector,omitempty" yaml:"fieldSelector,omitempty"`

	// Limit 限制数量
	Limit int64 `json:"limit,omitempty" yaml:"limit,omitempty"`

	// Continue 继续标记
	Continue string `json:"continue,omitempty" yaml:"continue,omitempty"`
}

// ListMeta 定义列表元数据
type ListMeta struct {
	// ResourceVersion 资源版本
	ResourceVersion string `json:"resourceVersion,omitempty" yaml:"resourceVersion,omitempty"`

	// Continue 继续标记
	Continue string `json:"continue,omitempty" yaml:"continue,omitempty"`

	// RemainingItemCount 剩余项目数量
	RemainingItemCount *int64 `json:"remainingItemCount,omitempty" yaml:"remainingItemCount,omitempty"`
}

// ResourceMetadata 辅助方法

// GetAnnotation 获取注解值
func (m *ResourceMetadata) GetAnnotation(key string) (string, bool) {
	if m.Annotations == nil {
		return "", false
	}
	value, exists := m.Annotations[key]
	return value, exists
}

// SetAnnotation 设置注解值
func (m *ResourceMetadata) SetAnnotation(key, value string) {
	if m.Annotations == nil {
		m.Annotations = make(map[string]string)
	}
	m.Annotations[key] = value
}

// DeleteAnnotation 删除注解
func (m *ResourceMetadata) DeleteAnnotation(key string) {
	if m.Annotations != nil {
		delete(m.Annotations, key)
	}
}

// GetLabel 获取标签值
func (m *ResourceMetadata) GetLabel(key string) (string, bool) {
	if m.Labels == nil {
		return "", false
	}
	value, exists := m.Labels[key]
	return value, exists
}

// SetLabel 设置标签值
func (m *ResourceMetadata) SetLabel(key, value string) {
	if m.Labels == nil {
		m.Labels = make(map[string]string)
	}
	m.Labels[key] = value
}

// DeleteLabel 删除标签
func (m *ResourceMetadata) DeleteLabel(key string) {
	if m.Labels != nil {
		delete(m.Labels, key)
	}
}

// IsBeingDeleted 判断资源是否正在被删除
func (m *ResourceMetadata) IsBeingDeleted() bool {
	return m.DeletionTimestamp != nil
}

// HasFinalizer 判断是否包含指定的终结器
func (m *ResourceMetadata) HasFinalizer(finalizer string) bool {
	return slices.Contains(m.Finalizers, finalizer)
}

// AddFinalizer 添加终结器
func (m *ResourceMetadata) AddFinalizer(finalizer string) {
	if !m.HasFinalizer(finalizer) {
		m.Finalizers = append(m.Finalizers, finalizer)
	}
}

// RemoveFinalizer 移除终结器
func (m *ResourceMetadata) RemoveFinalizer(finalizer string) {
	for i, f := range m.Finalizers {
		if f == finalizer {
			m.Finalizers = append(m.Finalizers[:i], m.Finalizers[i+1:]...)
			break
		}
	}
}

// SetImportMetadata 设置导入相关的元数据
func (m *ResourceMetadata) SetImportMetadata(source, originalID, notes string) {
	now := time.Now()
	m.SetAnnotation(AnnotationImportSource, source)
	m.SetAnnotation(AnnotationImportTime, now.Format(time.RFC3339))
	if originalID != "" {
		m.SetAnnotation(AnnotationOriginalID, originalID)
	}
	if notes != "" {
		m.SetAnnotation(AnnotationMigrationNotes, notes)
	}
}

// GetImportSource 获取导入来源
func (m *ResourceMetadata) GetImportSource() (string, bool) {
	return m.GetAnnotation(AnnotationImportSource)
}

// GetOriginalID 获取原始ID
func (m *ResourceMetadata) GetOriginalID() (string, bool) {
	return m.GetAnnotation(AnnotationOriginalID)
}
