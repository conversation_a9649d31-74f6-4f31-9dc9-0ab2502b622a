package commands

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/urfave/cli/v2"
	clientv3 "go.etcd.io/etcd/client/v3"

	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/apiserver"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/logger"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/pkg/config"
)

// NewAPIServerCommand 创建 API 服务器命令
func NewAPIServerCommand() *cli.Command {
	return &cli.Command{
		Name:  "apiserver",
		Usage: "启动 MCDN API 服务器",
		Flags: []cli.Flag{
			&cli.StringFlag{
				Name:  "bind",
				Value: ":8080",
				Usage: "服务器绑定地址",
			},
		},
		Action: func(c *cli.Context) error {
			return runAPIServer(c)
		},
	}
}

func runAPIServer(c *cli.Context) error {
	// 加载配置
	configPath := c.String("config")
	if configPath != "" {
		err := config.LoadFile(configPath)
		if err != nil {
			return fmt.Errorf("读取配置文件失败: %w", err)
		}
	} else {
		// 使用默认配置加载机制
		config.Init()
	}

	cfg := config.GetConfig()

	// 初始化日志
	log := logger.NewLogger()

	// 创建 etcd 客户端
	etcdClient, err := clientv3.New(clientv3.Config{
		Endpoints: cfg.Etcd.Endpoints,
	})
	if err != nil {
		return fmt.Errorf("创建 etcd 客户端失败: %w", err)
	}
	defer etcdClient.Close()

	// 创建 API 服务器
	server := apiserver.New(apiserver.Config{
		EtcdClient: etcdClient,
		Logger:     log,
	})

	// 设置 Gin 模式
	gin.SetMode(gin.ReleaseMode)

	// 创建路由
	router := gin.New()
	router.Use(gin.Recovery())

	// 注册 API 路由
	server.RegisterRoutes(router)

	// 创建 HTTP 服务器
	httpServer := &http.Server{
		Addr:    c.String("bind"),
		Handler: router,
	}

	// 启动服务器
	go func() {
		log.Info("启动 MCDN API 服务器", "addr", c.String("bind"))
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Error("API 服务器启动失败", "error", err)
		}
	}()

	// 等待退出信号
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	<-sigCh

	log.Info("收到退出信号，正在关闭 API 服务器...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := httpServer.Shutdown(ctx); err != nil {
		log.Error("API 服务器关闭失败", "error", err)
		return err
	}

	log.Info("API 服务器已关闭")
	return nil
}
