package commands

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/urfave/cli/v2"
	"gitlab.lilithgame.com/yunwei/pkg/logger"

	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/version"
	conf "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/pkg/config"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/pkg/worker"
)

// NewRunCommand 创建运行命令
func NewRunCommand() *cli.Command {
	return &cli.Command{
		Name:  "run",
		Usage: "启动 worker",
		Before: func(c *cli.Context) error {
			if err := conf.LoadFile(c.String("config")); err != nil {
				// 如果文件不存在，继续使用默认
				return err
			}
			cfg := conf.GetConfig()
			level := cfg.Log.Level
			if c.<PERSON>("debug") {
				level = "debug"
			}
			if cfg.Log.Path != "" {
				logger.InitFileLogger(cfg.Log.Path, level)
			} else {
				logger.InitLogger(os.Stdout, level)
			}
			logger.Info("配置加载完成", "path", c.String("config"))
			return nil
		},
		Action: func(c *cli.Context) error {
			cfg := conf.GetConfig()
			logger.Info("启动 mcdn", "version", version.Version, "env", cfg.Env)

			ctx, cancel := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
			defer cancel()

			w := worker.New(worker.Options{Env: cfg.Env})
			errCh := make(chan error, 1)
			go func() {
				if err := w.Run(ctx); err != nil {
					errCh <- err
				}
				close(errCh)
			}()

			select {
			case <-ctx.Done():
				logger.Info("收到退出信号，开始优雅关闭")
			case err := <-errCh:
				if err != nil {
					logger.Error("运行出错", "err", err)
				}
			}
			sctx, cancel2 := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel2()
			if err := w.Shutdown(sctx); err != nil {
				logger.Error("优雅关闭失败", "err", err)
			}
			logger.Info("退出完成")
			return nil
		},
	}
}
