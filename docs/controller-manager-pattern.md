# Controller-Manager 设计模式

## 概述

本项目借鉴了 Kubernetes Operator 的 Controller-Manager 设计模式，提供了优雅的控制器注册和管理方式。

## 设计理念

### K8s Operator 模式

```go
// K8s Operator 中的典型代码
func (r *GuestbookReconciler) SetupWithManager(mgr ctrl.Manager) error {
    return ctrl.NewControllerManagedBy(mgr).
            For(&webappv1.Guestbook{}).
            Complete(r)
}
```

### 我们的实现

```go
// MCDN 项目中的对应实现
func (c *Controller) SetupWithManager(mgr *controller.Manager, vendorMgr vendor.Manager) error {
    return controller.NewControllerManagedBy(mgr).
        Named("route-controller").
        For("route").
        WithEtcdPrefix("/mcdn/policy/").
        WithVendorManager(vendorMgr).
        Complete(c)
}
```

## 核心组件

### 1. Controller 接口

```go
type Controller interface {
    GetName() string
    GetResourceType() string 
    Reconcile(ctx context.Context, key string, obj any) error
    Start(ctx context.Context) error
    Stop() error
}
```

### 2. Manager 管理器

- 统一管理所有控制器的生命周期
- 提供共享资源（etcd 客户端、日志等）
- 协调控制器的启动顺序和停止流程

### 3. ControllerBuilder 构建器

- 提供流式 API 配置控制器
- 简化控制器的创建和注册过程
- 增强代码可读性和可维护性

## 使用方式

### 方式一：SetupWithManager（推荐）

```go
// 创建控制器
routeController := route.NewController(etcdClient, vendorMgr, log)

// 使用优雅的语法糖注册
if err := routeController.SetupWithManager(manager, vendorMgr); err != nil {
    return fmt.Errorf("设置 Route 控制器失败: %w", err)
}
```

### 方式二：直接注册（向下兼容）

```go
// 传统方式，直接注册到管理器
routeController := route.NewController(etcdClient, vendorMgr, log)
if err := manager.RegisterController(routeController); err != nil {
    return fmt.Errorf("注册 Route 控制器失败: %w", err)
}
```

### 方式三：纯 Builder 模式（高级配置）

```go
routeController := route.NewController(etcdClient, vendorMgr, log)
err = controller.NewControllerManagedBy(manager).
    Named("route-controller").
    For("route").
    WithEtcdPrefix("/mcdn/policy/").
    WithVendorManager(vendorMgr).
    Complete(routeController)
```

## 优势对比

### 传统方式的问题

```go
// 繁琐的手动配置
manager := controller.NewManager(etcdClient, log)
routeController := route.NewController(etcdClient, vendorMgr, log)
manager.RegisterController(routeController)
dnsController := dns.NewController(etcdClient, log, vendorMgr)  
manager.RegisterController(dnsController)
// ... 重复代码
```

### 新模式的优势

```go
// 清晰的声明式配置
routeController := route.NewController(etcdClient, vendorMgr, log)
routeController.SetupWithManager(manager, vendorMgr)

dnsController := dns.NewController(etcdClient, log, vendorMgr)
dnsController.SetupWithManager(manager, vendorMgr)
```

## 设计优势

### 1. **关注点分离**

- **Controller**：专注于特定资源的业务逻辑
- **Manager**：专注于控制器的生命周期管理
- **Builder**：专注于控制器的配置和组装

### 2. **依赖注入**

- Manager 自动提供共享的依赖（etcd 客户端、日志等）
- 控制器无需关心这些基础设施的创建和管理

### 3. **统一管理**

- 所有控制器通过 Manager 统一启动和停止
- 提供一致的错误处理和日志记录
- 支持优雅关闭和资源清理

### 4. **扩展性强**

- 新增控制器只需实现 Controller 接口
- Builder 模式支持灵活的配置扩展
- 支持中间件和插件机制

### 5. **代码优雅**

- 声明式的 API 风格
- 链式调用提高可读性
- 减少样板代码

## 实际应用场景

### 多控制器协调

```go
func setupControllers(manager *controller.Manager, vendorMgr vendor.Manager) error {
    // 路由控制器 - 处理流量路由策略
    routeController := route.NewController(etcdClient, vendorMgr, log)
    if err := routeController.SetupWithManager(manager, vendorMgr); err != nil {
        return err
    }

    // DNS 控制器 - 处理域名解析
    dnsController := dns.NewController(etcdClient, log, vendorMgr)
    if err := dnsController.SetupWithManager(manager, vendorMgr); err != nil {
        return err
    }

    // CDN 分发控制器 - 处理内容分发
    distributionController := distribution.NewController(etcdClient, vendorMgr, log)
    if err := distributionController.SetupWithManager(manager, vendorMgr); err != nil {
        return err
    }

    return nil
}
```

### 控制器依赖管理

```go
// Builder 可以扩展支持控制器间的依赖关系
controller.NewControllerManagedBy(manager).
    Named("route-controller").
    For("route").
    DependsOn("dns-controller").  // 依赖 DNS 控制器先启动
    WithEtcdPrefix("/mcdn/policy/").
    Complete(routeController)
```

## 与 K8s Operator 的对比

| 特性 | K8s Operator | MCDN 项目 |
|------|--------------|-----------|
| 资源发现 | Kubernetes API | etcd Watch |
| 事件驱动 | Informer/Controller | etcd Events |
| 状态管理 | Custom Resource Status | etcd KV + Status |
| 协调循环 | Reconcile Loop | Event Handler |
| 配置方式 | For(&v1.Pod{}) | For("route") |
| 依赖注入 | Manager | Manager |
| 生命周期 | Manager.Start() | Manager.Start() |

## 总结

这种设计模式的引入为项目带来了：

1. **更好的代码组织**：清晰的职责分离和模块化设计
2. **更高的开发效率**：减少样板代码，提供一致的开发模式
3. **更强的可维护性**：统一的错误处理和日志记录
4. **更好的可扩展性**：支持新控制器的快速接入
5. **更优雅的 API**：声明式配置，提高代码可读性

这正是 Kubernetes 生态系统成功的设计模式之一，值得在企业级项目中推广使用。
