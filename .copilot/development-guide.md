# MCDN 开发指南

## 新增控制器的标准流程

### 1. 定义资源类型 (`api/v1/`)

```go
// 定义资源结构
type MyResource struct {
    Metadata ResourceMetadata `json:"metadata"`
    Spec     MyResourceSpec   `json:"spec"`
    Status   MyResourceStatus `json:"status"`
}

// 实现 Resource 接口
type MyResourceResource struct {
    *MyResource
}

func (r *MyResourceResource) GetKind() string {
    return "MyResource"
}

func (r *MyResourceResource) GetEtcdPrefix() string {
    return "/mcdn/myresource/"
}
```

### 2. 实现控制器 (`internal/resource/myresource/`)

```go
type Controller struct {
    name         string
    resourceType string
    etcdClient   *clientv3.Client
    logger       logger.Logger
    vendorMgr    vendor.Manager
    watcher      watch.Watcher
}

// 实现 Controller 接口的所有方法
func (c *Controller) GetName() string { return c.name }
func (c *Controller) GetResourceType() string { return c.resourceType }
func (c *Controller) Reconcile(ctx context.Context, key string, obj any) error { /* 实现协调逻辑 */ }
func (c *Controller) Start(ctx context.Context) error { /* 启动逻辑 */ }
func (c *Controller) Stop() error { /* 停止逻辑 */ }

// 实现 SetupWithManager 方法
func (c *Controller) SetupWithManager(mgr controller.ManagerInterface) error {
    return controller.NewControllerManagedBy(mgr).
        Named("myresource-controller").
        For(&apiv1.MyResourceResource{}).
        Complete(c)
}
```

### 3. 注册控制器 (`pkg/app/commands/controller.go`)

```go
myResourceController := myresource.NewController(etcdClient, vendorMgr, log)
if err := myResourceController.SetupWithManager(manager); err != nil {
    return fmt.Errorf("设置 MyResource 控制器失败: %w", err)
}
```

## 开发环境设置

1. 确保已安装 Go 开发环境（推荐 Go 1.19+）
2. 安装 `task` 工具（<https://taskfile.dev/installation/）>
3. 克隆仓库
4. 进入 mcdn 目录
5. 安装依赖：`task tidy`

## 构建和运行

项目使用 `Taskfile.yml` 来管理构建、测试和运行任务。以下是一些常用的命令：

### 查看所有可用任务

```bash
task --list
```

### 构建项目

```bash
task build
```

### 运行项目

```bash
task run
```

### 开发模式运行

```bash
task dev
```

### 使用配置文件运行

```bash
./bin/mcdn --config /path/to/config.yaml
```

或者通过环境变量指定配置文件：

```bash
MCDN_CONFIG=/path/to/config.yaml ./bin/mcdn
```

## 配置管理

项目使用 `viper` 进行配置管理，支持多种配置源：

1. 配置文件（默认为 `config.yaml`）
2. 环境变量
3. 默认值

配置文件结构示例：

```yaml
env: dev
log:
  path: ./log/app.log
  level: debug
worker:
  concurrency: 4
  queue: default
```

配置加载顺序：

1. 默认配置值
2. 配置文件中的值
3. 环境变量中的值

## 测试

运行项目测试：

```bash
task test
```

或者直接使用 Go 命令：

```bash
go test ./...
```

## 代码质量

### 代码格式化

项目使用 `goimports` 和 `gofumpt` 进行代码格式化：

```bash
task format
```

### 代码检查

项目使用 `golangci-lint` 进行代码检查：

```bash
task lint
```

### 完整的构建流程

执行完整的构建流程，包括清理、格式化、代码检查、测试和构建：

```bash
task all
```

## 代码提交规范

遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```bash
# 功能特性
feat(controller): 新增 DNS 控制器支持多厂商

# 问题修复
fix(vendor): 修复 Akamai API 认证问题

# 重构
refactor(builder): 优化控制器构建器的类型安全性

# 文档更新
docs(api): 更新 API 接口文档

# 性能优化
perf(watch): 优化 etcd 事件监听性能

# 测试相关
test(controller): 添加控制器单元测试
```

## 性能优化建议

1. **并发处理**

   - 使用 Worker Pool 模式处理大量任务
   - 合理设置并发数量，避免资源竞争

2. **缓存策略**

   - 缓存频繁访问的配置信息
   - 使用本地缓存减少 etcd 访问

3. **连接池管理**

   - 复用 HTTP 客户端连接
   - 合理设置超时和重试策略

4. **内存管理**

   - 及时释放不再使用的资源
   - 使用对象池减少 GC 压力

## 贡献

如果您想为项目做贡献，请确保遵循项目的编码标准，并在提交前运行测试。
