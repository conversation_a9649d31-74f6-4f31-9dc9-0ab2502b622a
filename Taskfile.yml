version: '3'

vars:
  APP_NAME: mcdn
  BUILD_DIR: bin
  CMD_DIR: ./cmd/mcdn
  VERSION:
    sh: git describe --tags --always --dirty 2>/dev/null || echo "dev"
  COMMIT:
    sh: git rev-parse --short HEAD 2>/dev/null || echo "unknown"
  BUILD_TIME:
    sh: date -u '+%Y-%m-%d_%H:%M:%S'
  GOFLAGS: -trimpath
  LDFLAGS: -ldflags "-s -w"
  GCFLAGS: -gcflags "-N -l"
  EXE_NAME: mcdn
  GOOS:
    sh: go env GOOS
  GOARCH:
    sh: go env GOARCH

env:
  CGO_ENABLED: 0

tasks:
  default:
    desc: 显示可用任务
    cmds:
      - task --list

  tidy:
    desc: 整理 Go modules
    cmds:
      - go mod tidy

  lint:
    desc: 运行代码检查
    deps: [install-tools]
    cmds:
      - golangci-lint run --timeout=3m ./...

  install-tools:
    desc: 安装开发工具
    cmds:
      - go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
      - go install golang.org/x/tools/cmd/goimports@latest
      - go install mvdan.cc/gofumpt@latest
    status:
      - command -v golangci-lint
      - command -v goimports
      - command -v gofumpt

  format:
    desc: 格式化代码
    deps: [install-tools]
    cmds:
      - goimports -w .
      - gofumpt -l -w .

  clean:
    desc: 清理构建产物
    aliases: [c]
    cmds:
      - rm -rf {{.BUILD_DIR}}
      - go clean -cache

  build:
    desc: 构建二进制文件（优化大小）
    deps: [tidy]
    cmds:
      - mkdir -p {{.BUILD_DIR}}
      - go build {{.GOFLAGS}} {{.GCFLAGS}} -ldflags "-s -w -extldflags '-static' -X 'gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/version.Version={{.VERSION}}'" -o {{.BUILD_DIR}}/{{.GOOS}}-{{.GOARCH}}/{{.EXE_NAME}} ./cmd/{{.EXE_NAME}}/
      - ls -lh {{.BUILD_DIR}}/{{.GOOS}}-{{.GOARCH}}/{{.EXE_NAME}}
    env:
      CGO_ENABLED: 0
      # GOOS: linux
      # GOARCH: amd64
    vars:
      GOFLAGS: -trimpath
      GCFLAGS: -gcflags "-N -l"
  build-compress:
    desc: 构建二进制文件并使用UPX压缩
    deps: [build]
    cmds:
      - |
        if command -v upx >/dev/null 2>&1; then
          echo "使用 UPX 压缩二进制文件..."
          upx --best --lzma {{.BUILD_DIR}}/{{.GOOS}}-{{.GOARCH}}/{{.EXE_NAME}}
          ls -lh {{.BUILD_DIR}}/{{.GOOS}}-{{.GOARCH}}/{{.EXE_NAME}}
        else
          echo "提示: 安装 UPX 可以进一步减小二进制文件大小"
        fi
    env:
      CGO_ENABLED: 0
      GOOS: linux
      GOARCH: amd64
    vars:
      GOFLAGS: -trimpath
      GCFLAGS: -gcflags "-N -l"
  run:
    desc: 运行程序
    deps: [build]
    cmds:
      - ./{{.BUILD_DIR}}/{{.GOOS}}-{{.GOARCH}}/{{.EXE_NAME}} run -c config.yaml

  dev:
    desc: 开发模式运行
    cmds:
      - go run {{.CMD_DIR}} run -c config.yaml

  test:
    desc: 运行测试
    cmds:
      - go test -v ./...

  all:
    desc: 执行完整的构建流程
    deps: [clean, format, lint, test, build, build-compress]
    cmds:
      - echo "构建完成!"