package alicdn

import (
	"context"
	"fmt"

	apiv1 "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/api/v1"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/vendor"
)

// CDNClient 阿里云 CDN 客户端
type CDNClient struct {
	accessKeyID     string
	secretAccessKey string
	region          string
}

// NewCDNClient 创建阿里云 CDN 客户端
func NewCDNClient(accessKeyID, secretAccessKey, region string) *CDNClient {
	return &CDNClient{
		accessKeyID:     accessKeyID,
		secretAccessKey: secretAccessKey,
		region:          region,
	}
}

// GetVendorType 返回CDN厂商类型
func (c *CDNClient) GetVendorType() apiv1.VendorType {
	return apiv1.CDNVendorAliyun
}

// ValidateConfig 验证配置
func (c *CDNClient) ValidateConfig(config map[string]any) error {
	if c.accessKeyID == "" {
		return fmt.Errorf("AccessKeyID 不能为空")
	}
	if c.secretAccessKey == "" {
		return fmt.Errorf("SecretAccessKey 不能为空")
	}
	return nil
}

// CreateDistribution 创建 CDN Distribution
func (c *CDNClient) CreateDistribution(ctx context.Context, config *vendor.DistributionRequest) (string, error) {
	// TODO: 实现阿里云 CDN Distribution 创建逻辑
	return fmt.Sprintf("alicloud-dist-%s", config.Domain), nil
}

// UpdateDistribution 更新 CDN Distribution
func (c *CDNClient) UpdateDistribution(ctx context.Context, distributionID string, config *vendor.DistributionRequest) error {
	// TODO: 实现阿里云 CDN Distribution 更新逻辑
	return nil
}

// DeleteDistribution 删除 CDN Distribution
func (c *CDNClient) DeleteDistribution(ctx context.Context, distributionID string) error {
	// TODO: 实现阿里云 CDN Distribution 删除逻辑
	return nil
}

// GetDistribution 获取 CDN Distribution
func (c *CDNClient) GetDistribution(ctx context.Context, distributionID string) (*vendor.DistributionInfo, error) {
	// TODO: 实现阿里云 CDN Distribution 查询逻辑
	return &vendor.DistributionInfo{
		ID:     distributionID,
		Status: "Active",
	}, nil
}

// ListDistributions 列出 CDN Distributions
func (c *CDNClient) ListDistributions(ctx context.Context) ([]*vendor.DistributionInfo, error) {
	// TODO: 实现阿里云 CDN Distribution 列表逻辑
	return []*vendor.DistributionInfo{}, nil
}
