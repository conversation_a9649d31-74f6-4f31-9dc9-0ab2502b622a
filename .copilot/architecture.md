# MCDN 架构设计文档

## 整体架构设计

MCDN Worker 采用事件驱动的微服务架构，主要包含以下核心模块：

```text
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Server    │    │   Controller    │    │  Vendor Client  │
│                 │    │    Manager      │    │                 │
│  - HTTP API     │    │                 │    │  - AWS CDN      │
│  - RESTful      │◄──►│  - Route Ctrl   │◄──►│  - Aliyun CDN   │
│  - Health Check │    │  - DNS Ctrl     │    │  - Akamai       │
│                 │    │  - Dist Ctrl    │    │  - GCP CDN      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │                        │
         │              ┌─────────────────┐                │
         │              │   etcd Watch    │                │
         └─────────────►│                 │◄───────────────┘
                        │  - Event Bus    │
                        │  - State Store  │
                        │  - Config Store │
                        └─────────────────┘
```

## 模块职责

### 1. API Server (`pkg/apiserver/`)

- 提供 RESTful API 接口
- 处理外部请求和响应
- 健康检查和监控

### 2. Controller Manager (`internal/controller/`)

- 管理所有控制器的生命周期
- 实现事件驱动的协调循环
- 提供统一的依赖注入

### 3. Resource Controllers (`internal/resource/`)

- Policy Controller: 流量路由策略管理
- DNS Controller: 域名解析管理
- Distribution Controller: 内容分发管理

### 4. Vendor Clients (`internal/vendor/`)

- 抽象化多云厂商 API
- 统一的接口定义
- 配置和认证管理

### 5. Watcher & Events (`internal/watch/`)

- etcd 事件监听
- 资源状态变更通知
- 异步事件处理

## Controller-Manager 设计模式

项目采用了类似 Kubernetes Operator 的 Controller-Manager 设计模式，实现了优雅的控制器管理：

### 核心组件

1. **Controller 接口** - 定义统一的控制器规范

   ```go
   type Controller interface {
       GetName() string
       GetResourceType() string
       Reconcile(ctx context.Context, key string, obj any) error
       Start(ctx context.Context) error
       Stop() error
   }
   ```

2. **ManagerInterface** - 管理器接口，实现依赖注入

   ```go
   type ManagerInterface interface {
       RegisterController(controller Controller) error
       GetEtcdClient() *clientv3.Client
       GetLogger() logger.Logger
       GetVendorManager() vendor.Manager
   }
   ```

3. **ControllerBuilder** - 提供流式 API 配置控制器

### 依赖倒置原则 (DIP) 实践

**核心理念：** 高层模块不应依赖低层模块，两者都应依赖抽象；抽象不应依赖细节，细节应依赖抽象。

**项目实现：**

1. **接口驱动设计**

   ```go
   // ✅ 控制器依赖抽象接口，而非具体实现
   func (c *Controller) SetupWithManager(mgr ManagerInterface) error
   
   // ❌ 避免直接依赖具体类型
   // func (c *Controller) SetupWithManager(mgr *Manager, vendor vendor.Manager)
   ```

2. **依赖注入模式**

   ```go
   // Manager 作为依赖提供者，统一管理所有依赖
   type Manager struct {
       etcdClient *clientv3.Client
       logger     logger.Logger
       vendorMgr  vendor.Manager
   }
   
   // 控制器通过接口获取所需依赖
   func NewControllerManagedBy(mgr ManagerInterface) *ControllerBuilder {
       return &ControllerBuilder{
           manager:    mgr,
           etcdClient: mgr.GetEtcdClient(),
           logger:     mgr.GetLogger(),
           vendorMgr:  mgr.GetVendorManager(),
       }
   }
   ```

3. **控制反转 (IoC)**
   - 控制器不再负责创建和管理依赖
   - Manager 负责依赖的生命周期管理
   - 通过接口实现松耦合

### 类型安全的资源管理

项目实现了类似 K8s 的类型安全资源定义：

```go
// 定义资源接口
type Resource interface {
    GetKind() string
    GetEtcdPrefix() string
}

// 具体资源实现
type RouteResource struct {
    *Route
}

func (r *RouteResource) GetKind() string {
    return "Route"
}

func (r *RouteResource) GetEtcdPrefix() string {
    return "/mcdn/policy/"
}
```

**For() 方法的智能类型推断：**

```go
// ✅ 类型安全的方式（推荐）
.For(&apiv1.RouteResource{})

// ✅ K8s 风格的 nil 指针
.For((*apiv1.RouteResource)(nil))

// ⚠️ 向下兼容的字符串方式
.For("route")
```

### 控制器注册的最佳实践

**推荐模式：**

```go
// 1. 最简洁的语法糖
routeController := route.NewController(etcdClient, vendorMgr, log)
routeController.SetupWithManager(manager)

// 2. 类型安全的 Builder 模式
controller.NewControllerManagedBy(manager).
    Named("route-controller").
    For(&apiv1.RouteResource{}).
    Complete(routeController)

// 3. K8s 风格
controller.NewControllerManagedBy(manager).
    Named("route-controller").
    For((*apiv1.RouteResource)(nil)).
    Complete(routeController)
```

## SOLID 原则应用

1. **单一职责原则 (SRP)**
   - Controller 专注业务逻辑
   - Manager 专注生命周期管理
   - Builder 专注配置组装

2. **开闭原则 (OCP)**
   - 通过接口扩展新功能
   - 无需修改现有代码

3. **里氏替换原则 (LSP)**
   - 接口实现可以互相替换
   - Mock 测试支持

4. **接口隔离原则 (ISP)**
   - 小而专注的接口定义
   - 避免臃肿的接口

5. **依赖倒置原则 (DIP)**
   - 依赖接口而非实现
   - 通过注入管理依赖

## 测试友好设计

```go
// 接口化设计便于 Mock 测试
func TestController(t *testing.T) {
    mockManager := &MockManager{
        etcdClient: mockEtcdClient,
        logger:     mockLogger,
        vendorMgr:  mockVendorManager,
    }
    
    controller := NewController()
    err := controller.SetupWithManager(mockManager)
    assert.NoError(t, err)
}
```

## 并发安全设计

1. **读写锁保护共享状态**

   ```go
   type Manager struct {
       controllers map[string]Controller
       mu          sync.RWMutex
   }
   ```

2. **原子操作和通道通信**

   ```go
   // 使用 channel 进行协程间通信
   eventChan := make(chan *watch.Event)
   stopCh := make(chan struct{})
   ```

这些设计模式和最佳实践确保了代码的：

- **可维护性** - 清晰的职责分离
- **可扩展性** - 接口驱动的设计
- **可测试性** - 依赖注入和接口抽象
- **类型安全** - 编译时验证
- **向后兼容** - 渐进式升级支持
