# MCDN 故障排查和调试指南

## 日志分析

### 查看特定控制器的日志

```bash
# 查看路由控制器的日志
grep "controller=route" /var/log/mcdn/app.log

# 查看DNS控制器的日志
grep "controller=dns" /var/log/mcdn/app.log

# 实时监控错误日志
tail -f /var/log/mcdn/app.log | grep ERROR

# 查看特定时间段的日志
grep "2024-01-01" /var/log/mcdn/app.log | grep ERROR
```

### 结构化日志查询

```bash
# 使用 jq 解析 JSON 格式日志
tail -f /var/log/mcdn/app.log | jq 'select(.level == "error")'

# 查看特定资源的操作日志
grep '"resource":"my-route"' /var/log/mcdn/app.log | jq .

# 按时间排序查看日志
cat /var/log/mcdn/app.log | jq -s 'sort_by(.timestamp)'
```

## 健康检查

### API 健康检查

```bash
# 基础健康检查
curl http://localhost:8080/health

# 详细健康状态
curl http://localhost:8080/health/detailed

# 检查控制器状态
curl http://localhost:8080/api/v1/controllers

# 检查特定控制器
curl http://localhost:8080/api/v1/controllers/route-controller
```

### 依赖服务检查

```bash
# 检查 etcd 连接
curl http://localhost:8080/health/etcd

# 检查厂商 API 连接状态
curl http://localhost:8080/health/vendors

# 检查 API 服务器状态
curl -I http://localhost:8080/api/v1/
```

## 性能监控

### 系统资源监控

```bash
# CPU 和内存使用情况
top -p $(pgrep mcdn)

# 详细的进程信息
ps aux | grep mcdn

# 网络连接状态
netstat -tlnp | grep mcdn

# 文件描述符使用情况
lsof -p $(pgrep mcdn) | wc -l
```

### 应用级性能指标

```bash
# etcd 连接和响应时间
curl http://localhost:8080/metrics | grep etcd

# 控制器协调循环执行时间
curl http://localhost:8080/metrics | grep reconcile

# API 请求响应时间
curl http://localhost:8080/metrics | grep http_request_duration
```

## 常见问题排查

### etcd 连接问题

**症状**：控制器无法启动，日志显示 etcd 连接失败

**排查步骤**：

```bash
# 1. 检查 etcd 服务状态
systemctl status etcd

# 2. 测试 etcd 连接
etcdctl --endpoints=http://localhost:2379 endpoint health

# 3. 检查网络连通性
telnet localhost 2379

# 4. 查看 mcdn 配置
cat config/config.yaml | grep -A 5 etcd
```

**解决方案**：

1. 确保 etcd 服务正常运行
2. 检查配置文件中的 etcd 地址
3. 验证网络连接和防火墙设置
4. 检查认证配置

### 控制器协调失败

**症状**：资源状态长时间不同步，协调循环报错

**排查步骤**：

```bash
# 1. 查看协调错误日志
grep "reconcile failed" /var/log/mcdn/app.log

# 2. 检查资源状态
curl http://localhost:8080/api/v1/routes/problematic-route

# 3. 查看控制器队列状态
curl http://localhost:8080/debug/controllers/route-controller/queue

# 4. 检查厂商 API 调用
grep "vendor.*error" /var/log/mcdn/app.log
```

**解决方案**：

1. 检查厂商 API 认证配置
2. 验证资源配置的正确性
3. 重启相关控制器
4. 清理失效的资源状态

### 内存泄漏问题

**症状**：应用内存使用持续增长

**排查步骤**：

```bash
# 1. 监控内存使用趋势
while true; do
  ps -p $(pgrep mcdn) -o pid,vsz,rss,pmem,time
  sleep 60
done

# 2. 检查 Go 运行时统计
curl http://localhost:8080/debug/pprof/heap

# 3. 生成内存 profile
go tool pprof http://localhost:8080/debug/pprof/heap

# 4. 检查 goroutine 泄漏
curl http://localhost:8080/debug/pprof/goroutine?debug=1
```

**解决方案**：

1. 分析内存 profile 找到泄漏点
2. 检查 goroutine 是否正确关闭
3. 验证缓存清理逻辑
4. 升级依赖库版本

## 调试工具

### pprof 性能分析

```bash
# 开启 pprof 服务器（在开发环境）
go run cmd/mcdn/main.go --pprof-addr=:6060

# CPU profile
go tool pprof http://localhost:6060/debug/pprof/profile?seconds=30

# 内存 profile
go tool pprof http://localhost:6060/debug/pprof/heap

# goroutine 分析
go tool pprof http://localhost:6060/debug/pprof/goroutine
```

### 调试端点

```bash
# 查看所有调试端点
curl http://localhost:8080/debug/

# 查看版本信息
curl http://localhost:8080/debug/version

# 查看配置信息（脱敏）
curl http://localhost:8080/debug/config

# 查看运行时统计
curl http://localhost:8080/debug/stats
```

### etcd 数据检查

```bash
# 查看所有 mcdn 相关的 key
etcdctl --endpoints=http://localhost:2379 get --prefix /mcdn/

# 查看特定资源
etcdctl --endpoints=http://localhost:2379 get /mcdn/policy/my-route

# 监听资源变化
etcdctl --endpoints=http://localhost:2379 watch --prefix /mcdn/policy/

# 清理失效数据
etcdctl --endpoints=http://localhost:2379 del /mcdn/policy/old-route
```

## 紧急处理程序

### 服务重启

```bash
# 优雅重启
kill -TERM $(pgrep mcdn)

# 强制重启
systemctl restart mcdn

# 检查重启后状态
curl http://localhost:8080/health
```

### 数据备份

```bash
# 备份 etcd 数据
etcdctl --endpoints=http://localhost:2379 snapshot save backup.db

# 验证备份
etcdctl --write-out=table snapshot status backup.db

# 恢复数据（谨慎操作）
etcdctl snapshot restore backup.db
```

### 回滚操作

```bash
# 查看资源历史版本
curl http://localhost:8080/api/v1/routes/my-route?includeHistory=true

# 回滚到上一个版本
curl -X PUT http://localhost:8080/api/v1/routes/my-route/rollback \
  -H "Content-Type: application/json" \
  -d '{"targetRevision": "previous"}'
```

## 监控告警

### 关键指标监控

1. **服务可用性**

   - API 响应时间
   - 健康检查状态
   - 错误率

2. **资源使用**

   - CPU 使用率
   - 内存使用量
   - 文件描述符数量

3. **业务指标**

   - 控制器协调成功率
   - etcd 连接状态
   - 厂商 API 调用成功率

### 告警规则示例

```yaml
# Prometheus 告警规则
groups:
  - name: mcdn
    rules:
      - alert: MCDNServiceDown
        expr: up{job="mcdn"} == 0
        for: 1m
        annotations:
          summary: "MCDN 服务不可用"
          
      - alert: MCDNHighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        annotations:
          summary: "MCDN 错误率过高"
```

通过这些工具和方法，可以快速定位和解决 MCDN 系统中的各种问题。
