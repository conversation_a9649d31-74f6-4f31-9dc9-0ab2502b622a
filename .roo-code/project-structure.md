# 项目结构说明

## 总体架构

MCDN 是一个基于 Go 语言开发的多云CDN调度管理平台，采用微服务架构，使用 etcd 作为配置存储和服务发现。

## 目录结构

```
mcdn/
├── .roo-code/              # Roo-Code 配置文件
├── api/                    # API 定义和类型
│   └── v1/                 # API v1 版本
│       ├── types.go        # 通用类型定义
│       ├── dns.go          # DNS 相关类型
│       ├── distribution.go # CDN 分发相关类型
│       └── policy.go        # 路由相关类型
├── cmd/                    # 应用程序入口
│   └── mcdn/               # 主程序
│       └── main.go
├── pkg/                    # 公共包
│   ├── app/                # 应用框架
│   ├── apiserver/          # API 服务器
│   ├── config/             # 配置管理
│   ├── version/            # 版本信息
│   └── worker/             # 任务执行器
├── internal/               # 内部包（不对外暴露）
│   ├── controller/         # 控制器核心逻辑
│   │   ├── controller.go   # 基础控制器
│   │   ├── registry.go     # 控制器注册管理
│   │   └── watch.go        # etcd 监听器
│   ├── logger/             # 日志组件
│   │   └── logger.go
│   ├── resource/           # 资源控制器
│   │   ├── distribution/   # CDN 分发控制器
│   │   ├── dns/            # DNS 控制器
│   │   └── route/          # 路由控制器
│   └── vendor/             # 云厂商客户端
├── config.example.yaml     # 配置文件示例
├── config.schema.json      # 配置文件 JSON Schema
├── docker-compose.dev.yml  # 开发环境 Docker Compose
├── go.mod                  # Go 模块定义
├── go.sum                  # Go 模块校验和
├── Taskfile.yml           # Task 构建配置
└── README.md              # 项目说明
```

## 核心组件

### 1. API 层 (`api/v1/`)

定义了项目中使用的所有数据结构和 API 类型：

- **types.go**: 通用类型，如 `ResourceMetadata`
- **dns.go**: DNS 相关类型，如 `DNSZone`, `DNSRecord`
- **distribution.go**: CDN 分发相关类型
- **policy.go**: 路由调度相关类型

### 2. 应用程序入口 (`cmd/`)

- **mcdn**: 主程序入口，包含多个子命令（apiserver、controller、run 等）

### 3. 公共包 (`pkg/`)

#### 应用框架 (`pkg/app/`)

- 提供 CLI 应用框架和命令管理

#### API 服务器 (`pkg/apiserver/`)

- HTTP 服务器实现和路由管理

#### 配置管理 (`pkg/config/`)

- 配置结构定义、默认值和验证

#### 任务执行器 (`pkg/worker/`)

- 后台任务执行和队列管理

### 4. 内部逻辑 (`internal/`)

#### 控制器框架 (`internal/controller/`)

- **controller.go**: 基础控制器接口和实现
- **registry.go**: 控制器注册和管理
- **watch.go**: etcd 资源监听和事件处理

#### 资源控制器 (`internal/resource/`)

每个资源类型都有独立的控制器：

- **dns/**: DNS 资源管理（Zone, Record）
- **distribution/**: CDN 分发资源管理
- **route/**: 路由调度管理

#### 云厂商客户端 (`internal/vendor/`)

- 抽象各云厂商的 API 接口
- 提供统一的调用方式

## 设计模式

### 1. 控制器模式

```go
type Controller interface {
    GetName() string
    GetResourceType() string
    Start(ctx context.Context) error
    Stop() error
    Reconcile(ctx context.Context, key string, obj any) error
}
```

### 2. 观察者模式

通过 etcd Watch API 监听资源变化，触发相应的处理逻辑。

### 3. 工厂模式

控制器注册和创建使用工厂模式，便于扩展新的资源类型。

## 数据流

```text
etcd 资源变更 → Watcher → Controller → 业务逻辑 → 云厂商 API → 状态更新 → etcd
```

## 扩展指南

### 添加新的资源类型

1. 在 `api/v1/` 中定义新的资源类型
2. 在 `internal/resource/` 中创建对应的控制器
3. 在 `internal/controller/registry.go` 中注册新控制器
4. 实现 `Controller` 接口的所有方法

### 添加新的云厂商支持

1. 在 `internal/vendor/` 中实现厂商接口
2. 在资源控制器中集成新的厂商客户端
3. 更新配置文件 schema

## 配置管理

- 使用 Viper 进行配置管理
- 支持 YAML 格式配置文件
- 提供 JSON Schema 验证
- 支持环境变量覆盖

## 日志规范

- 使用结构化日志
- 统一的日志格式和级别
- 关键操作必须记录日志
- 错误信息包含足够的上下文

## 测试策略

- 单元测试覆盖核心逻辑
- 集成测试验证 etcd 交互
- 使用 testify 测试框架
- Mock 外部依赖（云厂商 API）
