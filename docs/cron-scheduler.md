# MCDN Cron 调度控制器

## 概述

MCDN 的 Cron 调度控制器提供基于时间的自动化路由切换能力。通过定义 `RouteType: Scheduled` 的路由策略，可以实现按照 Cron 表达式自动切换 CDN、调整路由策略等功能。

## 架构设计

### 设计原则

遵循 Kubernetes 控制器模式：

- `internal/controller/` - 控制器基础框架和调度器实现
- `internal/resource/policy/` - 路由资源的具体控制器实现
- 通用的 CronScheduler 可被其他资源控制器复用

### 核心组件

1. **CronScheduler** (`internal/controller/scheduler.go`)
   - 基于 `github.com/robfig/cron/v3` 的通用调度器
   - 支持秒级精度的 Cron 表达式
   - 提供任务生命周期管理（添加、更新、删除）
   - 统计和监控支持

2. **Policy Controller** (`internal/resource/policy/controller.go`)
   - 处理 `RouteType: Scheduled` 类型的路由策略
   - 集成 CronScheduler 进行定时调度
   - 支持 CDN 切换、路由调整等操作

3. **RouteTask**
   - 实现 `ScheduledTask` 接口
   - 封装具体的路由切换逻辑

## 使用方法

### 1. Cron 表达式格式

支持标准的 6 位 Cron 表达式（包含秒）：

```text
┌─────────────── 秒 (0 - 59)
│ ┌───────────── 分钟 (0 - 59)
│ │ ┌─────────── 小时 (0 - 23)
│ │ │ ┌───────── 日 (1 - 31)
│ │ │ │ ┌─────── 月 (1 - 12)
│ │ │ │ │ ┌───── 星期 (0 - 6) (星期日到星期六)
│ │ │ │ │ │
* * * * * *
```

### 2. 路由策略配置

#### 基础定时切换示例

```yaml
apiVersion: v1
kind: Route
metadata:
  name: daily-cdn-switch
  namespace: default
spec:
  name: "每日 CDN 切换策略"
  description: "每天凌晨 6 点切换到 B CDN"
  type: Scheduled
  enabled: true
  
  # 调度配置
  schedule:
    cronExpression: "0 0 6 * * *"  # 每天 6:00 AM
    timezone: "Asia/Shanghai"
    repeat: true
  
  # 策略规则
  rules:
    - id: "switch-to-b-cdn"
      condition:
        type: Time
        operator: EQUALS
        value: "06:00"
      action:
        type: Route
        target:
          kind: Distribution
          name: b-cdn-distribution
```

#### 复杂调度示例

```yaml
# 工作日高峰期使用高性能 CDN
apiVersion: v1
kind: Route
metadata:
  name: weekday-peak-hours
spec:
  type: Scheduled
  schedule:
    cronExpression: "0 0 19 * * 1-5"  # 工作日 19:00
    timezone: "Asia/Shanghai"
  rules:
    - id: "peak-hours-cdn"
      action:
        type: Route
        target:
          kind: Distribution
          name: high-performance-cdn
```

### 3. 常用 Cron 表达式

| 描述 | Cron 表达式 | 说明 |
|------|------------|------|
| 每天 6:00 AM | `0 0 6 * * *` | 每日凌晨 6 点执行 |
| 工作日 9:00 AM | `0 0 9 * * 1-5` | 周一到周五 9 点执行 |
| 每小时整点 | `0 0 * * * *` | 每小时的 0 分 0 秒执行 |
| 每 30 分钟 | `0 */30 * * * *` | 每 30 分钟执行一次 |
| 周末 12:00 AM | `0 0 0 * * 0,6` | 周六周日午夜执行 |

## 功能特性

### 1. 时区支持

```yaml
schedule:
  cronExpression: "0 0 6 * * *"
  timezone: "Asia/Shanghai"  # 支持标准时区
```

### 2. 执行统计

控制器会自动记录：

- 执行次数 (`ExecutionCount`)
- 成功次数 (`SuccessCount`)
- 失败次数 (`FailureCount`)
- 上次执行时间 (`LastExecutionTime`)
- 下次执行时间 (`NextExecutionTime`)

### 3. 生命周期管理

- **激活** - 验证配置，添加到调度器
- **更新** - 动态更新调度任务
- **删除** - 从调度器中移除任务

### 4. 错误处理

- 无效 Cron 表达式会在验证阶段被拒绝
- 任务执行失败会记录错误统计
- 支持重试机制和超时控制

## 监控和调试

### 1. 日志

```bash
# 查看调度器启动日志
INFO 启动 Cron 调度器

# 查看任务添加日志
INFO 成功添加调度任务 task=route-task-daily-cdn-switch schedule="0 0 6 * * *"

# 查看任务执行日志
INFO 开始执行调度任务 task=route-task-daily-cdn-switch runCount=1
INFO 调度任务执行成功 task=route-task-daily-cdn-switch duration=2.3s
```

### 2. 统计信息

调度器提供统计 API：

```go
stats := scheduler.GetStats()
// stats 包含：
// - totalTasks: 总任务数
// - activeTasks: 活跃任务数  
// - totalRuns: 总执行次数
// - totalErrors: 总错误次数
// - tasks: 详细任务信息列表
```

## 最佳实践

### 1. 命名规范

- 路由策略名称使用描述性名称：`daily-cdn-switch`、`weekend-routing`
- 规则 ID 使用动作描述：`switch-to-b-cdn`、`enable-geo-routing`

### 2. 时间配置

- 始终指定时区：`timezone: "Asia/Shanghai"`
- 避免在 59 秒执行，预留缓冲时间
- 考虑夏令时对定时任务的影响

### 3. 资源引用

- 确保 `action.target` 引用的资源存在
- 使用明确的资源类型：`kind: Distribution`

### 4. 测试建议

- 使用较短的 Cron 表达式进行功能测试
- 监控日志确认任务正常执行
- 验证 CDN 切换是否生效

## 扩展性

### 1. 添加新的调度类型

实现 `ScheduledTask` 接口：

```go
type MyTask struct {
    name     string
    schedule string
    enabled  bool
}

func (t *MyTask) Execute(ctx context.Context) error { /* 实现逻辑 */ }
func (t *MyTask) GetName() string { return t.name }
func (t *MyTask) GetSchedule() string { return t.schedule }
func (t *MyTask) IsEnabled() bool { return t.enabled }
```

### 2. 集成其他资源控制器

其他资源控制器可以复用 `CronScheduler`：

```go
// DNS 控制器中使用调度器
dnsController.scheduler = controller.NewCronScheduler(logger)
```

## 故障排除

### 1. 常见问题

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 任务不执行 | Cron 表达式错误 | 验证表达式格式 |
| 时间不准确 | 时区配置错误 | 检查 timezone 设置 |
| 任务执行失败 | 目标资源不存在 | 确认资源引用正确 |

### 2. 调试命令

```bash
# 检查控制器日志
kubectl logs -f mcdn-controller

# 验证 Cron 表达式
# 可以使用在线工具或编写测试程序验证
```

## 未来规划

1. **Web UI** - 提供可视化的调度任务管理界面
2. **事件通知** - 任务执行结果通知（Webhook、邮件等）
3. **条件调度** - 基于外部条件的动态调度
4. **批量操作** - 支持批量启停、更新调度任务
