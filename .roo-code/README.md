# MCDN 项目 Roo-Code 配置

这个目录包含了 MCDN 项目的 Roo-Code 指令文件，用于指导 AI 助手理解项目结构、代码风格和开发规范。

## 文件说明

- `code-style.md` - 代码风格和格式化规范
- `golang-config.md` - Go 语言版本和依赖管理
- `project-structure.md` - 项目结构和架构说明
- `build-config.md` - 构建和任务配置
- `development-guide.md` - 开发指南和最佳实践
- `roo-instructions.md` - 主配置文件，整合所有指令

## 使用方式

AI 助手在处理此项目时应该：

1. 首先阅读 `roo-instructions.md` 获取总体指导
2. 根据具体任务参考相应的专项配置文件
3. 严格遵循项目的代码风格和架构规范
