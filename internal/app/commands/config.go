package commands

import (
	"fmt"

	"github.com/urfave/cli/v2"

	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/pkg/config"
)

// NewConfigCommand 创建配置命令
func NewConfigCommand() *cli.Command {
	return &cli.Command{
		Name:  "config",
		Usage: "打印当前配置 (合并默认值)",
		Action: func(ctx *cli.Context) error {
			path := ctx.String("config")
			err := config.LoadFile(path) // 忽略不存在错误?
			if err != nil {
				return err
			}

			cfg := config.GetConfig()
			fmt.Printf("Load from %s:\n", path)
			fmt.Println(cfg)
			return nil
		},
	}
}
