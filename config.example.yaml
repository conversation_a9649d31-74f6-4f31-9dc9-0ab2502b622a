# yaml-language-server: $schema=./config.schema.json

# 示例配置文件，请复制为 config.yaml 后按需修改
env: dev
log:
  level: info
  path: ""  # 输出到控制台，或指定文件路径如 "./log/mcdn.log"

# API 服务器配置
api:
  enabled: true
  host: "0.0.0.0"
  port: 8080

# Controller 配置
controller:
  enabled: true
  workers: 2

# ETCD 配置
etcd:
  endpoints:
    - "127.0.0.1:2379"
  timeout: 5s

# Worker 配置
worker:
  concurrency: 4
  queue: default
