package v1

import "time"

// PolicySpec 定义调度策略的规格
type PolicySpec struct {
	// Name 策略名称
	Name string `json:"name" yaml:"name"`
	// Description 策略描述
	Description string `json:"description,omitempty" yaml:"description,omitempty"`
	// Type 策略类型
	Type PolicyType `json:"type" yaml:"type"`
	// Rules 策略规则
	Rules []PolicyRule `json:"rules" yaml:"rules"`
	// Priority 优先级 (数值越大优先级越高)
	Priority int32 `json:"priority,omitempty" yaml:"priority,omitempty"`
	// Enabled 是否启用
	Enabled bool `json:"enabled" yaml:"enabled"`
	// Schedule 调度配置 (用于定时策略)
	Schedule *ScheduleConfig `json:"schedule,omitempty" yaml:"schedule,omitempty"`
}

// PolicyType 定义策略类型
type PolicyType string

const (
	// PolicyTypeLoadBalancing 负载均衡策略
	PolicyTypeLoadBalancing PolicyType = "LoadBalancing"
	// PolicyTypeFailover 故障转移策略
	PolicyTypeFailover PolicyType = "Failover"
	// PolicyTypeGeoRouting 地理路由策略
	PolicyTypeGeoRouting PolicyType = "GeoRouting"
	// PolicyTypeHealthCheck 健康检查策略
	PolicyTypeHealthCheck PolicyType = "HealthCheck"
	// PolicyTypeRateLimit 限流策略
	PolicyTypeRateLimit PolicyType = "RateLimit"
	// PolicyTypeScheduled 定时策略
	PolicyTypeScheduled PolicyType = "Scheduled"
	// PolicyTypeCanary 灰度发布策略
	PolicyTypeCanary PolicyType = "Canary"
)

// PolicyRule 定义策略规则
type PolicyRule struct {
	// ID 规则 ID
	ID string `json:"id" yaml:"id"`
	// Condition 触发条件
	Condition PolicyCondition `json:"condition" yaml:"condition"`
	// Action 执行动作
	Action PolicyAction `json:"action" yaml:"action"`
	// Weight 权重 (用于负载均衡)
	Weight int32 `json:"weight,omitempty" yaml:"weight,omitempty"`
	// Priority 优先级
	Priority int32 `json:"priority,omitempty" yaml:"priority,omitempty"`
}

// PolicyCondition 定义策略条件
type PolicyCondition struct {
	// Type 条件类型
	Type ConditionType `json:"type" yaml:"type"`
	// Operator 操作符
	Operator ConditionOperator `json:"operator" yaml:"operator"`
	// Value 条件值
	Value string `json:"value" yaml:"value"`
	// Values 条件值列表 (用于 IN, NOT_IN 操作符)
	Values []string `json:"values,omitempty" yaml:"values,omitempty"`
}

// ConditionType 定义条件类型
type ConditionType string

const (
	// ConditionTypeGeoLocation 地理位置
	ConditionTypeGeoLocation ConditionType = "GeoLocation"
	// ConditionTypeUserAgent 用户代理
	ConditionTypeUserAgent ConditionType = "UserAgent"
	// ConditionTypeRequestPath 请求路径
	ConditionTypeRequestPath ConditionType = "RequestPath"
	// ConditionTypeRequestHeader 请求头
	ConditionTypeRequestHeader ConditionType = "RequestHeader"
	// ConditionTypeSourceIP 源 IP
	ConditionTypeSourceIP ConditionType = "SourceIP"
	// ConditionTypeTime 时间
	ConditionTypeTime ConditionType = "Time"
	// ConditionTypeHealthStatus 健康状态
	ConditionTypeHealthStatus ConditionType = "HealthStatus"
	// ConditionTypeRequestRate 请求频率
	ConditionTypeRequestRate ConditionType = "RequestRate"
)

// ConditionOperator 定义条件操作符
type ConditionOperator string

const (
	ConditionOperatorEquals      ConditionOperator = "EQUALS"       // 等于
	ConditionOperatorNotEquals   ConditionOperator = "NOT_EQUALS"   // 不等于
	ConditionOperatorContains    ConditionOperator = "CONTAINS"     // 包含
	ConditionOperatorNotContains ConditionOperator = "NOT_CONTAINS" // 不包含
	ConditionOperatorStartsWith  ConditionOperator = "STARTS_WITH"  // 以...开始
	ConditionOperatorEndsWith    ConditionOperator = "ENDS_WITH"    // 以...结束
	ConditionOperatorIn          ConditionOperator = "IN"           // 在列表中
	ConditionOperatorNotIn       ConditionOperator = "NOT_IN"       // 不在列表中
	ConditionOperatorGreaterThan ConditionOperator = "GREATER_THAN" // 大于
	ConditionOperatorLessThan    ConditionOperator = "LESS_THAN"    // 小于
	ConditionOperatorRegex       ConditionOperator = "REGEX"        // 正则匹配
)

// PolicyAction 定义策略动作
type PolicyAction struct {
	// Type 动作类型
	Type ActionType `json:"type" yaml:"type"`
	// Target 目标资源
	Target ResourceReference `json:"target" yaml:"target"`
	// Parameters 动作参数
	Parameters map[string]any `json:"parameters,omitempty" yaml:"parameters,omitempty"`
}

// ActionType 定义动作类型
type ActionType string

const (
	// ActionTypeRoute 路由到指定资源
	ActionTypeRoute ActionType = "Route"
	// ActionTypeRedirect 重定向
	ActionTypeRedirect ActionType = "Redirect"
	// ActionTypeBlock 阻止访问
	ActionTypeBlock ActionType = "Block"
	// ActionTypeRateLimit 限制频率
	ActionTypeRateLimit ActionType = "RateLimit"
	// ActionTypeSetHeader 设置请求头
	ActionTypeSetHeader ActionType = "SetHeader"
	// ActionTypeRemoveHeader 移除请求头
	ActionTypeRemoveHeader ActionType = "RemoveHeader"
	// ActionTypeModifyResponse 修改响应
	ActionTypeModifyResponse ActionType = "ModifyResponse"
	// ActionTypeLog 记录日志
	ActionTypeLog ActionType = "Log"
)

// ScheduleConfig 定义调度配置
type ScheduleConfig struct {
	// CronExpression Cron 表达式
	CronExpression string `json:"cronExpression,omitempty" yaml:"cronExpression,omitempty"`
	// StartTime 开始时间
	StartTime *time.Time `json:"startTime,omitempty" yaml:"startTime,omitempty"`
	// EndTime 结束时间
	EndTime *time.Time `json:"endTime,omitempty" yaml:"endTime,omitempty"`
	// Timezone 时区
	Timezone string `json:"timezone,omitempty" yaml:"timezone,omitempty"`
	// Repeat 是否重复执行
	Repeat bool `json:"repeat,omitempty" yaml:"repeat,omitempty"`
}

// PolicyStatus 定义调度策略的状态
type PolicyStatus struct {
	// Phase 当前阶段
	Phase PolicyPhase `json:"phase" yaml:"phase"`
	// Message 状态消息
	Message string `json:"message,omitempty" yaml:"message,omitempty"`
	// LastSyncTime 最后同步时间
	LastSyncTime *time.Time `json:"lastSyncTime,omitempty" yaml:"lastSyncTime,omitempty"`
	// LastExecutionTime 最后执行时间
	LastExecutionTime *time.Time `json:"lastExecutionTime,omitempty" yaml:"lastExecutionTime,omitempty"`
	// NextExecutionTime 下次执行时间
	NextExecutionTime *time.Time `json:"nextExecutionTime,omitempty" yaml:"nextExecutionTime,omitempty"`
	// ExecutionCount 执行次数
	ExecutionCount int64 `json:"executionCount,omitempty" yaml:"executionCount,omitempty"`
	// SuccessCount 成功次数
	SuccessCount int64 `json:"successCount,omitempty" yaml:"successCount,omitempty"`
	// FailureCount 失败次数
	FailureCount int64 `json:"failureCount,omitempty" yaml:"failureCount,omitempty"`
	// Conditions 状态条件
	Conditions []ResourceCondition `json:"conditions,omitempty" yaml:"conditions,omitempty"`
}

// PolicyPhase 定义调度策略的生命周期阶段
type PolicyPhase string

const (
	PolicyPhasePending  PolicyPhase = "Pending"  // 等待激活
	PolicyPhaseActive   PolicyPhase = "Active"   // 活跃状态
	PolicyPhaseInactive PolicyPhase = "Inactive" // 非活跃状态
	PolicyPhaseUpdating PolicyPhase = "Updating" // 更新中
	PolicyPhaseDeleting PolicyPhase = "Deleting" // 删除中
	PolicyPhaseFailed   PolicyPhase = "Failed"   // 失败状态
	PolicyPhaseDraft    PolicyPhase = "Draft"    // 草稿状态
)

// Policy 定义调度策略资源
type Policy struct {
	// Metadata 元数据
	Metadata ResourceMetadata `json:"metadata" yaml:"metadata"`
	// Spec 规格
	Spec PolicySpec `json:"spec" yaml:"spec"`
	// Status 状态
	Status PolicyStatus `json:"status" yaml:"status"`
}

// PolicyList 定义策略列表
type PolicyList struct {
	// Metadata 列表元数据
	Metadata ListMeta `json:"metadata" yaml:"metadata"`
	// Items 策略项目
	Items []Policy `json:"items" yaml:"items"`
}
