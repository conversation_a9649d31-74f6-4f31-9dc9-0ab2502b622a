package dns

import (
	"encoding/json"

	apiv1 "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/api/v1"
)

// DNSRecordResource 定义 DNS Record 资源的内部表示
type DNSRecordResource struct {
	*apiv1.DNSRecord
}

// NewDNSRecordResource 创建新的 DNS Record 资源
func NewDNSRecordResource(record *apiv1.DNSRecord) *DNSRecordResource {
	return &DNSRecordResource{
		DNSRecord: record,
	}
}

// GetKey 获取资源的 etcd key
func (r *DNSRecordResource) GetKey() string {
	return "/mcdn/dns/records/" + r.Metadata.Name
}

// GetName 获取资源名称
func (r *DNSRecordResource) GetName() string {
	return r.Metadata.Name
}

// GetNamespace 获取资源命名空间
func (r *DNSRecordResource) GetNamespace() string {
	return r.Metadata.Namespace
}

// ToJSON 转换为 JSON
func (r *DNSRecordResource) ToJSON() ([]byte, error) {
	return json.Marshal(r.DNSRecord)
}

// FromJSON 从 JSON 创建资源
func (r *DNSRecordResource) FromJSON(data []byte) error {
	record := &apiv1.DNSRecord{}
	if err := json.Unmarshal(data, record); err != nil {
		return err
	}
	r.DNSRecord = record
	return nil
}

// DNSResourceInterface 定义 DNS 资源的通用接口
type DNSResourceInterface interface {
	GetKey() string
	GetName() string
	GetNamespace() string
	ToJSON() ([]byte, error)
	FromJSON([]byte) error
}

// ResourceType 定义资源类型
type ResourceType string

const (
	// ResourceTypeDNSRecord DNS Record 资源类型
	ResourceTypeDNSRecord ResourceType = "DNSRecord"
)

// GetResourceType 获取 DNS Record 资源类型
func (r *DNSRecordResource) GetResourceType() ResourceType {
	return ResourceTypeDNSRecord
}

// ValidateRecord 验证 DNS Record 配置
func (r *DNSRecordResource) ValidateRecord() error {
	if r.Spec.Zone == "" {
		return NewValidationError("zone", "所属 Zone 不能为空")
	}
	if r.Spec.Name == "" {
		return NewValidationError("name", "记录名称不能为空")
	}
	if r.Spec.Type == "" {
		return NewValidationError("type", "记录类型不能为空")
	}
	if r.Spec.Value == "" {
		return NewValidationError("value", "记录值不能为空")
	}
	return nil
}

// ValidationError 定义验证错误
type ValidationError struct {
	Field   string
	Message string
}

// NewValidationError 创建验证错误
func NewValidationError(field, message string) *ValidationError {
	return &ValidationError{
		Field:   field,
		Message: message,
	}
}

// Error 实现 error 接口
func (e *ValidationError) Error() string {
	return "validation error for field '" + e.Field + "': " + e.Message
}
