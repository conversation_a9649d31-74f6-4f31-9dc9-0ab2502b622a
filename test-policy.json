{"metadata": {"name": "test-failover-route", "labels": {"env": "test", "team": "cdn"}}, "spec": {"name": "测试故障转移路由", "description": "测试用的故障转移路由策略", "type": "Failover", "enabled": true, "priority": 100, "rules": [{"id": "rule-1", "condition": {"type": "HealthStatus", "operator": "EQUALS", "value": "unhealthy"}, "action": {"type": "Route", "target": {"kind": "Distribution", "name": "backup-cdn", "namespace": "default"}, "parameters": {"reason": "primary_cdn_unhealthy"}}, "weight": 100, "priority": 1}]}}