package dns

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	clientv3 "go.etcd.io/etcd/client/v3"

	apiv1 "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/api/v1"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/controller"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/logger"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/vendor"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/watch"
)

// Controller DNS 控制器
type Controller struct {
	name          string
	resourceType  string
	etcdClient    *clientv3.Client
	logger        logger.Logger
	watcher       watch.Watcher
	vendorManager vendor.Manager
	stopCh        chan struct{}
}

// NewController 创建 DNS 控制器
func NewController(etcdClient *clientv3.Client, log logger.Logger, vendorManager vendor.Manager) *Controller {
	ctrl := &Controller{
		name:          "dns-controller",
		resourceType:  "dns",
		etcdClient:    etcdClient,
		logger:        log,
		vendorManager: vendorManager,
		stopCh:        make(chan struct{}),
	}

	// 创建 watcher
	prefix := fmt.Sprintf("/mcdn/%s/", ctrl.resourceType)
	ctrl.watcher = watch.NewWatcher(etcdClient, prefix, log)

	return ctrl
}

// SetupWithManager 设置控制器与管理器 - 类似于 K8s Operator 的模式
func (c *Controller) SetupWithManager(mgr controller.ManagerInterface) error {
	// 创建资源实例用于类型信息
	dnsRecordResource := apiv1.NewDNSRecordResource(nil)

	return controller.NewControllerManagedBy(mgr).
		Named("dns-controller").
		For(dnsRecordResource).
		Complete(c)
}

// Start 启动控制器
func (c *Controller) Start(ctx context.Context) error {
	c.logger.Info("启动控制器", "name", c.name, "resourceType", c.resourceType)

	// 启动 watcher
	eventCh, err := c.watcher.Start(ctx)
	if err != nil {
		return fmt.Errorf("启动 watcher 失败: %w", err)
	}

	// 处理事件
	go c.processEvents(ctx, eventCh)

	return nil
}

// Stop 停止控制器
func (c *Controller) Stop() error {
	c.logger.Info("停止控制器", "name", c.name)
	close(c.stopCh)
	return c.watcher.Stop()
}

// GetName 返回控制器名称
func (c *Controller) GetName() string {
	return c.name
}

// GetResourceType 返回控制器管理的资源类型
func (c *Controller) GetResourceType() string {
	return c.resourceType
}

// processEvents 处理 etcd 事件
func (c *Controller) processEvents(ctx context.Context, eventCh <-chan *watch.Event) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-c.stopCh:
			return
		case event := <-eventCh:
			if event == nil {
				continue
			}
			if err := c.handleResource(ctx, event.Key, event.Value); err != nil {
				c.logger.Error("处理资源失败",
					"key", event.Key,
					"type", string(event.Type),
					"error", err)
			}
		}
	}
}

// logError 统一的错误日志记录
func (c *Controller) logError(operation string, err error, fields ...any) {
	args := append([]any{"operation", operation, "error", err}, fields...)
	c.logger.Error("操作失败", args...)
}

// logInfo 统一的信息日志记录
func (c *Controller) logInfo(operation string, fields ...any) {
	args := append([]any{"operation", operation}, fields...)
	c.logger.Info("执行操作", args...)
}

// logWarn 统一的警告日志记录
func (c *Controller) logWarn(operation string, msg string, fields ...any) {
	args := append([]any{"operation", operation, "message", msg}, fields...)
	c.logger.Warn("警告", args...)
}

// Reconcile 执行 DNS 资源协调逻辑
func (c *Controller) Reconcile(ctx context.Context, key string, obj any) error {
	c.logInfo("reconcile", "key", key)

	// 解析事件数据
	eventData, ok := obj.([]byte)
	if !ok {
		return fmt.Errorf("无效的事件数据类型")
	}

	// 判断资源类型并处理
	if err := c.handleResource(ctx, key, eventData); err != nil {
		c.logError("reconcile", err, "key", key)
		return err
	}

	c.logInfo("reconcile", "key", key, "status", "completed")
	return nil
}

// handleResource 处理 DNS 资源
func (c *Controller) handleResource(ctx context.Context, key string, data []byte) error {
	// 只处理 DNS Record
	if c.isDNSRecordKey(key) {
		return c.handleDNSRecord(ctx, key, data)
	}

	return fmt.Errorf("未知的 DNS 资源类型: %s", key)
}

// isDNSRecordKey 判断是否为 DNS Record 的 key
func (c *Controller) isDNSRecordKey(key string) bool {
	return len(key) > len("/mcdn/dns/records/") && key[:len("/mcdn/dns/records/")] == "/mcdn/dns/records/"
}

// handleDNSRecord 处理 DNS Record 资源
func (c *Controller) handleDNSRecord(ctx context.Context, key string, data []byte) error {
	c.logInfo("handleDNSRecord", "key", key)

	// 解析 DNS Record
	record := &apiv1.DNSRecord{}
	if err := json.Unmarshal(data, record); err != nil {
		return fmt.Errorf("解析 DNS Record 失败: %w", err)
	}

	// 创建资源包装器
	recordResource := NewDNSRecordResource(record)

	// 验证配置
	if err := recordResource.ValidateRecord(); err != nil {
		return c.updateRecordStatus(ctx, recordResource, apiv1.DNSRecordPhaseFailed, err.Error())
	}

	// 由于不管理DNSZone，这里需要从配置或其他方式获取厂商信息
	// 暂时使用默认厂商，实际使用时应该从配置中获取
	vendorType := apiv1.DNSVendorDnspod // 默认使用dnspod，实际应该从配置获取

	// 获取云厂商客户端
	vendorClient, err := c.vendorManager.GetDNSClient(vendorType)
	if err != nil {
		return c.updateRecordStatus(ctx, recordResource, apiv1.DNSRecordPhaseFailed,
			fmt.Sprintf("获取DNS厂商客户端失败: %v", err))
	}

	// 根据当前状态执行相应操作
	switch record.Status.Phase {
	case "", apiv1.DNSRecordPhasePending:
		return c.createDNSRecord(ctx, recordResource, vendorClient)
	case apiv1.DNSRecordPhaseCreating:
		return c.checkDNSRecordStatus(ctx, recordResource, vendorClient)
	case apiv1.DNSRecordPhaseActive:
		return c.updateDNSRecord(ctx, recordResource, vendorClient)
	case apiv1.DNSRecordPhaseDeleting:
		return c.deleteDNSRecord(ctx, recordResource, vendorClient)
	default:
		c.logWarn("handleDNSRecord", "未知的 DNS Record 状态",
			"phase", string(record.Status.Phase))
		return nil
	}
}

// updateRecordStatus 更新 DNS Record 状态
func (c *Controller) updateRecordStatus(ctx context.Context, record *DNSRecordResource, phase apiv1.DNSRecordPhase, message string) error {
	now := time.Now()
	record.Status.Phase = phase
	record.Status.Message = message
	record.Status.LastSyncTime = &now

	// 序列化并保存到 etcd
	data, err := record.ToJSON()
	if err != nil {
		c.logError("updateRecordStatus", err, "name", record.GetName())
		return fmt.Errorf("序列化 DNS Record 失败: %w", err)
	}

	// 保存到 etcd
	key := fmt.Sprintf("/mcdn/dns/records/%s", record.GetName())
	if _, err = c.etcdClient.Put(ctx, key, string(data)); err != nil {
		c.logError("updateRecordStatus", err, "name", record.GetName())
		return fmt.Errorf("保存到 etcd 失败: %w", err)
	}

	c.logInfo("updateRecordStatus",
		"name", record.GetName(),
		"phase", string(phase),
		"message", message)

	return nil
}

func (c *Controller) createDNSRecord(ctx context.Context, record *DNSRecordResource, client vendor.DNSClient) error {
	c.logInfo("createDNSRecord", "name", record.GetName(), "domain", record.Spec.Name, "zone", record.Spec.Zone)

	// 更新状态为创建中
	if err := c.updateRecordStatus(ctx, record, apiv1.DNSRecordPhaseCreating, "正在创建 DNS Record"); err != nil {
		return err
	}

	// 由于不管理DNSZone，这里使用Zone名称作为zoneID
	// 实际使用时应该有一个映射关系或者从配置中获取真实的zoneID
	zoneID := record.Spec.Zone // 简化处理，实际应该映射到真实的厂商zoneID

	// 调用云厂商 API 创建记录
	recordID, err := client.CreateDNSRecord(ctx, zoneID, &vendor.DNSRecordRequest{
		Name:  record.Spec.Name,
		Type:  record.Spec.Type,
		Value: record.Spec.Value,
		TTL:   record.Spec.TTL,
	})
	if err != nil {
		return c.updateRecordStatus(ctx, record, apiv1.DNSRecordPhaseFailed,
			fmt.Sprintf("创建 DNS Record 失败: %v", err))
	}

	// 更新状态
	record.Status.VendorRecordID = recordID
	return c.updateRecordStatus(ctx, record, apiv1.DNSRecordPhaseActive, "DNS Record 创建成功")
}

func (c *Controller) checkDNSRecordStatus(ctx context.Context, record *DNSRecordResource, client vendor.DNSClient) error {
	c.logInfo("checkDNSRecordStatus", "name", record.GetName(), "zone", record.Spec.Zone)

	// 检查记录状态
	recordInfo, err := client.GetDNSRecord(ctx, record.Status.VendorRecordID)
	if err != nil {
		return c.updateRecordStatus(ctx, record, apiv1.DNSRecordPhaseFailed,
			fmt.Sprintf("检查 DNS Record 状态失败: %v", err))
	}

	// 根据状态更新
	if recordInfo.Status == "ACTIVE" {
		return c.updateRecordStatus(ctx, record, apiv1.DNSRecordPhaseActive, "DNS Record 已就绪")
	}

	// 继续等待
	return c.updateRecordStatus(ctx, record, apiv1.DNSRecordPhaseCreating, "DNS Record 正在创建中")
}

func (c *Controller) updateDNSRecord(ctx context.Context, record *DNSRecordResource, client vendor.DNSClient) error {
	c.logInfo("updateDNSRecord", "name", record.GetName(), "zone", record.Spec.Zone)

	// 更新记录
	if err := client.UpdateDNSRecord(ctx, record.Status.VendorRecordID, &vendor.DNSRecordRequest{
		Name:  record.Spec.Name,
		Type:  record.Spec.Type,
		Value: record.Spec.Value,
		TTL:   record.Spec.TTL,
	}); err != nil {
		return c.updateRecordStatus(ctx, record, apiv1.DNSRecordPhaseFailed,
			fmt.Sprintf("更新 DNS Record 失败: %v", err))
	}

	return c.updateRecordStatus(ctx, record, apiv1.DNSRecordPhaseActive, "DNS Record 更新成功")
}

func (c *Controller) deleteDNSRecord(ctx context.Context, record *DNSRecordResource, client vendor.DNSClient) error {
	c.logInfo("deleteDNSRecord", "name", record.GetName(), "zone", record.Spec.Zone)

	// 删除记录
	if err := client.DeleteDNSRecord(ctx, record.Status.VendorRecordID); err != nil {
		return c.updateRecordStatus(ctx, record, apiv1.DNSRecordPhaseFailed,
			fmt.Sprintf("删除 DNS Record 失败: %v", err))
	}

	// 从 etcd 中删除
	key := fmt.Sprintf("/mcdn/dns/records/%s", record.GetName())
	if _, err := c.etcdClient.Delete(ctx, key); err != nil {
		c.logError("deleteDNSRecord", err, "name", record.GetName())
		return fmt.Errorf("从 etcd 删除 Record 失败: %w", err)
	}

	return nil
}
