package config

import (
	"log"
	"sync"

	"github.com/spf13/viper"
)

var (
	cfg       *Config
	once      sync.Once
	cfgLocker = new(sync.RWMutex)
)

// Init 确保配置按默认机制加载（conf.* -> ./ 或 ./config）
func Init() { once.Do(loadconfig) }

// LoadFile 强制从指定文件加载 (支持多次调用覆盖)
func LoadFile(path string) error {
	viper.Reset()
	viper.SetConfigFile(path)
	if err := viper.ReadInConfig(); err != nil {
		return err
	}
	tmpConfig := defaultConfig
	if err := viper.Unmarshal(&tmpConfig); err != nil {
		return err
	}
	cfgLocker.Lock()
	cfg = &tmpConfig
	cfgLocker.Unlock()
	return nil
}

// GetConfig 取得当前配置指针（懒加载）
func GetConfig() *Config {
	once.Do(loadconfig)
	cfgLocker.RLock()
	defer cfgLocker.RUnlock()
	return cfg
}

func loadconfig() {
	viper.SetConfigName("conf")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")

	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			log.Fatalf("read config failed: %s", err.Error())
		}
	}

	tmpConfig := defaultConfig
	if err := viper.Unmarshal(&tmpConfig); err != nil {
		log.Fatalf("unable decode config file, %v", err)
	}
	cfgLocker.Lock()
	cfg = &tmpConfig
	cfgLocker.Unlock()
}
