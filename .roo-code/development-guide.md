# 开发指南和最佳实践

## 开发环境设置

### 必需工具

1. **Go 1.24.6+** - 编程语言运行时
2. **Task** - 构建工具
3. **Git** - 版本控制
4. **VS Code** (推荐) - 代码编辑器

### 开发工具安装

```bash
# 安装开发工具
task install-tools

# 手动安装
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
go install golang.org/x/tools/cmd/goimports@latest
go install mvdan.cc/gofumpt@latest
```

### VS Code 扩展推荐

- **Go** - Go 语言支持
- **Task** - Taskfile 支持
- **GitLens** - Git 增强
- **YAML** - YAML 文件支持

## 开发工作流

### 1. 开始开发

```bash
# 克隆项目
git clone <repository-url>
cd mcdn

# 安装依赖
go mod download

# 安装开发工具
task install-tools

# 运行开发环境
task dev
```

### 2. 代码开发

```bash
# 格式化代码
task format

# 检查代码质量
task lint

# 运行测试
task test

# 完整检查
task all
```

### 3. 提交代码

```bash
# 确保代码质量
task all

# 提交代码
git add .
git commit -m "feat: 添加新功能"
git push
```

## 代码组织原则

### 包设计原则

1. **单一职责** - 每个包只负责一个明确的功能
2. **最小接口** - 暴露最少必要的公共接口
3. **依赖倒置** - 高层模块不依赖低层模块
4. **接口隔离** - 客户端不应依赖不需要的接口

### 目录组织

```
internal/
├── controller/     # 控制器框架（通用）
├── resource/       # 资源控制器（具体实现）
│   ├── dns/        # DNS 资源
│   ├── distribution/ # CDN 分发资源
│   └── route/      # 路由资源
├── vendor/         # 云厂商客户端
├── config/         # 配置管理
└── logger/         # 日志组件
```

## 编码规范

### 接口设计

```go
// 好的接口设计 - 小而专注
type Logger interface {
    Info(msg string, keysAndValues ...any)
    Error(msg string, keysAndValues ...any)
}

// 避免 - 过大的接口
type BadInterface interface {
    DoEverything()
    HandleAllCases()
    // ... 太多方法
}
```

### 错误处理

```go
// 推荐 - 包装错误并提供上下文
func (c *Controller) processResource(key string) error {
    resource, err := c.getResource(key)
    if err != nil {
        return fmt.Errorf("获取资源失败 [key=%s]: %w", key, err)
    }
    
    if err := c.validateResource(resource); err != nil {
        return fmt.Errorf("验证资源失败 [key=%s]: %w", key, err)
    }
    
    return nil
}

// 避免 - 忽略错误
func badExample() {
    result, _ := someOperation() // 不要这样做
    // ...
}
```

### 并发安全

```go
// 推荐 - 使用互斥锁保护共享状态
type SafeCounter struct {
    mu    sync.RWMutex
    count int
}

func (c *SafeCounter) Increment() {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.count++
}

func (c *SafeCounter) Value() int {
    c.mu.RLock()
    defer c.mu.RUnlock()
    return c.count
}
```

### 上下文使用

```go
// 推荐 - 正确传递和使用 context
func (c *Controller) Start(ctx context.Context) error {
    // 创建可取消的子上下文
    childCtx, cancel := context.WithCancel(ctx)
    defer cancel()
    
    // 启动后台任务
    go c.backgroundTask(childCtx)
    
    // 等待上下文取消
    <-ctx.Done()
    return ctx.Err()
}

func (c *Controller) backgroundTask(ctx context.Context) {
    ticker := time.NewTicker(time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ctx.Done():
            return
        case <-ticker.C:
            // 执行定期任务
        }
    }
}
```

## 测试策略

### 单元测试

```go
func TestController_ProcessResource(t *testing.T) {
    tests := []struct {
        name    string
        key     string
        want    error
        setup   func(*Controller)
    }{
        {
            name: "成功处理资源",
            key:  "test-resource",
            want: nil,
            setup: func(c *Controller) {
                // 设置测试数据
            },
        },
        {
            name: "资源不存在",
            key:  "non-existent",
            want: ErrResourceNotFound,
            setup: func(c *Controller) {
                // 设置测试条件
            },
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            c := NewController()
            if tt.setup != nil {
                tt.setup(c)
            }
            
            err := c.ProcessResource(tt.key)
            if !errors.Is(err, tt.want) {
                t.Errorf("ProcessResource() error = %v, want %v", err, tt.want)
            }
        })
    }
}
```

### 集成测试

```go
func TestIntegration_ControllerWithEtcd(t *testing.T) {
    // 启动测试用的 etcd
    etcdServer := startTestEtcd(t)
    defer etcdServer.Close()
    
    // 创建控制器
    controller := NewController(etcdServer.Client())
    
    // 测试完整流程
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()
    
    err := controller.Start(ctx)
    assert.NoError(t, err)
}
```

### Mock 使用

```go
//go:generate mockgen -source=interface.go -destination=mock.go

type MockVendorClient struct {
    mock.Mock
}

func (m *MockVendorClient) CreateDNSZone(ctx context.Context, domain string) (string, error) {
    args := m.Called(ctx, domain)
    return args.String(0), args.Error(1)
}

func TestWithMock(t *testing.T) {
    mockClient := new(MockVendorClient)
    mockClient.On("CreateDNSZone", mock.Anything, "example.com").Return("zone-123", nil)
    
    controller := NewController(mockClient)
    // 测试逻辑...
    
    mockClient.AssertExpectations(t)
}
```

## 性能优化

### 内存管理

```go
// 推荐 - 重用对象池
var bufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 0, 1024)
    },
}

func processData(data []byte) []byte {
    buf := bufferPool.Get().([]byte)
    defer bufferPool.Put(buf[:0])
    
    // 使用 buf 处理数据
    return result
}
```

### 并发控制

```go
// 推荐 - 使用 worker pool 限制并发
func (c *Controller) processResources(resources []Resource) error {
    const maxWorkers = 10
    semaphore := make(chan struct{}, maxWorkers)
    
    var wg sync.WaitGroup
    for _, resource := range resources {
        wg.Add(1)
        go func(r Resource) {
            defer wg.Done()
            semaphore <- struct{}{} // 获取信号量
            defer func() { <-semaphore }() // 释放信号量
            
            c.processResource(r)
        }(resource)
    }
    
    wg.Wait()
    return nil
}
```

## 调试技巧

### 日志调试

```go
// 使用结构化日志便于调试
c.logger.Debug("处理资源开始",
    "key", key,
    "resourceType", resourceType,
    "timestamp", time.Now())

// 记录关键状态变化
c.logger.Info("资源状态变更",
    "key", key,
    "oldPhase", oldPhase,
    "newPhase", newPhase,
    "reason", reason)
```

### 性能分析

```bash
# CPU 性能分析
go test -cpuprofile=cpu.prof -bench=. ./...
go tool pprof cpu.prof

# 内存分析
go test -memprofile=mem.prof -bench=. ./...
go tool pprof mem.prof

# 在线分析
import _ "net/http/pprof"
go func() {
    log.Println(http.ListenAndServe("localhost:6060", nil))
}()
```

## 部署和运维

### 健康检查

```go
func (s *Server) healthCheck(w http.ResponseWriter, r *http.Request) {
    status := map[string]interface{}{
        "status": "healthy",
        "timestamp": time.Now(),
        "version": version.Version,
    }
    
    // 检查关键组件
    if err := s.checkEtcdConnection(); err != nil {
        status["status"] = "unhealthy"
        status["error"] = err.Error()
        w.WriteHeader(http.StatusServiceUnavailable)
    }
    
    json.NewEncoder(w).Encode(status)
}
```

### 优雅关闭

```go
func (s *Server) gracefulShutdown() {
    c := make(chan os.Signal, 1)
    signal.Notify(c, os.Interrupt, syscall.SIGTERM)
    
    <-c
    s.logger.Info("收到关闭信号，开始优雅关闭")
    
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    if err := s.Shutdown(ctx); err != nil {
        s.logger.Error("关闭失败", "error", err)
    }
}
```

## 常见问题和解决方案

### 1. etcd 连接问题

```go
// 实现重连机制
func (c *Client) connectWithRetry(ctx context.Context) error {
    backoff := time.Second
    maxBackoff := time.Minute
    
    for {
        if err := c.connect(); err == nil {
            return nil
        }
        
        select {
        case <-ctx.Done():
            return ctx.Err()
        case <-time.After(backoff):
            backoff = min(backoff*2, maxBackoff)
        }
    }
}
```

### 2. 内存泄漏排查

```bash
# 定期检查内存使用
go tool pprof http://localhost:6060/debug/pprof/heap

# 查看 goroutine 泄漏
go tool pprof http://localhost:6060/debug/pprof/goroutine
```

### 3. 性能瓶颈分析

```bash
# 生成火焰图
go test -bench=. -cpuprofile=cpu.prof ./...
go tool pprof -http=:8080 cpu.prof
```

这个开发指南涵盖了项目开发的各个方面，为开发者提供了详细的指导和最佳实践。
