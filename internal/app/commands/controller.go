package commands

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/urfave/cli/v2"
	clientv3 "go.etcd.io/etcd/client/v3"

	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/controller"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/logger"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/resource/policy"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/vendor"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/pkg/config"
)

// NewControllerCommand 创建控制器命令
func NewControllerCommand() *cli.Command {
	return &cli.Command{
		Name:  "controller",
		Usage: "启动 MCDN 控制器",
		Action: func(c *cli.Context) error {
			return runController(c)
		},
	}
}

func runController(c *cli.Context) error {
	// 加载配置
	configPath := c.String("config")
	if configPath != "" {
		err := config.LoadFile(configPath)
		if err != nil {
			return fmt.Errorf("读取配置文件失败: %w", err)
		}
	} else {
		// 使用默认配置加载机制
		config.Init()
	}

	cfg := config.GetConfig()

	// 初始化日志
	log := logger.NewLogger()

	// 创建 etcd 客户端
	etcdClient, err := clientv3.New(clientv3.Config{
		Endpoints: cfg.Etcd.Endpoints,
	})
	if err != nil {
		return fmt.Errorf("创建 etcd 客户端失败: %w", err)
	}
	defer etcdClient.Close()

	// 创建 vendor 管理器
	vendorMgr := vendor.NewManager()

	// 创建控制器管理器
	manager := controller.NewManager(etcdClient, log, vendorMgr)

	// 方式一：使用新的 SetupWithManager 模式（推荐）- 类型安全且简洁
	policyController := policy.NewController(etcdClient, vendorMgr, log)
	if err := policyController.SetupWithManager(manager); err != nil {
		return fmt.Errorf("设置 Policy 控制器失败: %w", err)
	}

	// 方式二：使用传统的直接注册方式（向下兼容）
	// policyController := policy.NewController(etcdClient, vendorMgr, log)
	// if err := manager.RegisterController(policyController); err != nil {
	//     return fmt.Errorf("注册 Policy 控制器失败: %w", err)
	// }

	// 方式三：使用纯 Builder 模式 - 类型安全版本
	// import apiv1 "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/api/v1"
	// policyController := policy.NewController(etcdClient, vendorMgr, log)
	// err = controller.NewControllerManagedBy(manager).
	//     Named("policy-controller").
	//     For(&apiv1.PolicyResource{}).  // 类型安全！
	//     Complete(policyController)
	// if err != nil {
	//     return fmt.Errorf("设置 Policy 控制器失败: %w", err)
	// }

	// 方式四：支持 nil 指针语法（类似 K8s）
	// err = controller.NewControllerManagedBy(manager).
	//     Named("policy-controller").
	//     For((*apiv1.PolicyResource)(nil)).  // K8s 风格！
	//     Complete(policyController)

	// 注册其他资源控制器
	if err := controller.RegisterControllers(manager, vendorMgr); err != nil {
		return fmt.Errorf("注册控制器失败: %w", err)
	}

	// 创建上下文和信号处理
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 监听系统信号
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// 启动控制器管理器
	log.Info("启动 MCDN 控制器...")
	go func() {
		if err := manager.Start(ctx); err != nil {
			log.Error("控制器管理器启动失败", "error", err)
			cancel()
		}
	}()

	// 等待退出信号
	<-sigCh
	log.Info("收到退出信号，正在关闭...")
	cancel()

	return nil
}
