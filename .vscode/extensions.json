{
  "recommendations": [
    // Go 语言支持
    "golang.go",
    
    // YAML 支持
    "redhat.vscode-yaml",
    
    // Task Runner 支持
    "task.vscode-task",
    
    // Docker 支持
    "ms-vscode-remote.remote-containers",
    "ms-azuretools.vscode-docker",
    
    // Git 增强
    // "eamodio.gitlens",
    
    // JSON Schema 支持
    "ms-vscode.vscode-json",
    
    // 代码注释增强
    "aaron-bond.better-comments",
    
    // 错误镜头
    "usernamehw.errorlens",
    
    // REST API 测试（如果项目有 API）
    "humao.rest-client",
    
    // TODO 高亮
    "wayou.vscode-todo-highlight",
  ],
  "unwantedRecommendations": [
    // 不推荐的扩展（避免冲突或不必要的扩展）
    "ms-vscode.go"  // 旧版本的 Go 扩展，现在应该使用 golang.go
  ]
}
