# MCDN API 文档

这是一个使用 Go 语言开发的多云CDN调度管理平台，基于 etcd 实现资源监听和控制器模式，提供完整的 CDN 分发、DNS 解析和路由调度管理能力。

## 项目架构

MCDN 采用控制器模式（Controller Pattern），通过监听 etcd 中的资源变化来执行相应的调度任务。

### 核心组件

- **API Server**: 提供 RESTful API 接口
- **Controller Manager**: 管理各种资源控制器
- **Resource Controllers**: 处理具体的资源类型
  - Distribution Controller: CDN 分发管理
  - DNS Controller: DNS 解析管理
  - Policy Controller: 路由调度管理
- **Worker**: 任务执行引擎

## API 概览

MCDN 提供基于 REST 的 API 接口，支持 JSON 格式的数据交换。所有 API 都遵循 RESTful 设计原则。

### 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **Content-Type**: `application/json`
- **API 版本**: v1

### 支持的资源类型

1. **Distribution** - CDN 分发配置
2. **DNS Zone** - DNS 区域管理
3. **DNS Record** - DNS 记录管理
4. **Policy** - 路由调度策略

## API 端点

### 健康检查

#### GET /health

检查服务健康状态。

**响应示例:**

```json
{
  "status": "ok",
  "service": "mcdn-apiserver"
}
```

---

## Distribution API

CDN 分发配置管理 API。

### 端点列表

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/v1/distributions` | 获取所有 Distribution |
| POST | `/api/v1/distributions` | 创建新的 Distribution |
| GET | `/api/v1/distributions/{name}` | 获取指定 Distribution |
| PUT | `/api/v1/distributions/{name}` | 更新指定 Distribution |
| DELETE | `/api/v1/distributions/{name}` | 删除指定 Distribution |

### Distribution 数据结构

```json
{
  "metadata": {
    "name": "example-distribution",
    "namespace": "default",
    "labels": {
      "env": "production",
      "team": "cdn"
    },
    "annotations": {
      "mcdn.lilithgames.com/import-source": "aws"
    },
    "creationTimestamp": "2024-01-01T00:00:00Z"
  },
  "spec": {
    "domain": "cdn.example.com",
    "vendor": "aws_cdn",
    "enabled": true,
    "comment": "生产环境 CDN 分发",
    "origins": [
      {
        "id": "origin-1",
        "domainName": "origin.example.com",
        "originPath": "/content",
        "httpPort": 80,
        "httpsPort": 443,
        "originProtocolPolicy": "https-only",
        "weight": 100,
        "priority": 1
      }
    ],
    "cacheConfig": {
      "defaultTTL": 86400,
      "maxTTL": 31536000,
      "minTTL": 0,
      "queryStringCaching": "none",
      "cacheBehaviors": [
        {
          "pathPattern": "/api/*",
          "ttl": 0,
          "viewerProtocolPolicy": "https-only",
          "allowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "POST", "PATCH", "DELETE"],
          "cachedMethods": ["GET", "HEAD"]
        }
      ]
    },
    "sslConfig": {
      "certificateArn": "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012",
      "sslSupportMethod": "sni-only",
      "minimumProtocolVersion": "TLSv1.2_2021"
    },
    "vendorConfig": {
      "priceClass": "PriceClass_All",
      "httpVersion": "http2"
    }
  },
  "status": {
    "phase": "Active",
    "message": "Distribution is active and serving traffic",
    "lastSyncTime": "2024-01-01T12:00:00Z",
    "vendorDistributionId": "E1234567890123",
    "domainName": "d1234567890123.cloudfront.net",
    "status": "Deployed"
  }
}
```

### 支持的厂商类型

- `aws_cdn` - AWS CloudFront
- `gcp_media` - GCP Media CDN
- `aliyun_cdn` - 阿里云 CDN
- `akamai` - Akamai CDN

---

## DNS API

DNS 区域和记录管理 API。

### DNS Zone 端点

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/v1/dns/zones` | 获取所有 DNS Zone |
| POST | `/api/v1/dns/zones` | 创建新的 DNS Zone |
| GET | `/api/v1/dns/zones/{name}` | 获取指定 DNS Zone |
| PUT | `/api/v1/dns/zones/{name}` | 更新指定 DNS Zone |
| DELETE | `/api/v1/dns/zones/{name}` | 删除指定 DNS Zone |

### DNS Record 端点

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/v1/dns/records` | 获取所有 DNS Record |
| POST | `/api/v1/dns/records` | 创建新的 DNS Record |
| GET | `/api/v1/dns/records/{name}` | 获取指定 DNS Record |
| PUT | `/api/v1/dns/records/{name}` | 更新指定 DNS Record |
| DELETE | `/api/v1/dns/records/{name}` | 删除指定 DNS Record |

### DNS Zone 数据结构

```json
{
  "metadata": {
    "name": "example-zone",
    "namespace": "default",
    "labels": {
      "env": "production"
    }
  },
  "spec": {
    "domain": "example.com",
    "ttl": 300,
    "vendor": "dnspod",
    "vendorConfig": {
      "region": "ap-southeast-1"
    }
  },
  "status": {
    "phase": "Active",
    "message": "DNS Zone is active",
    "lastSyncTime": "2024-01-01T12:00:00Z",
    "vendorZoneId": "123456",
    "nameServers": [
      "ns1.dnspod.net",
      "ns2.dnspod.net"
    ]
  }
}
```

### DNS Record 数据结构

```json
{
  "metadata": {
    "name": "www-record",
    "namespace": "default"
  },
  "spec": {
    "zone": "example.com",
    "name": "www",
    "type": "A",
    "value": "192.168.1.100",
    "ttl": 300,
    "weight": 100
  },
  "status": {
    "phase": "Active",
    "message": "DNS Record is active",
    "lastSyncTime": "2024-01-01T12:00:00Z",
    "vendorRecordId": "789012"
  }
}
```

### 支持的 DNS 厂商

- `dnspod` - 腾讯云 DNSPod
- `aliyun_dns` - 阿里云 DNS
- `route53` - AWS Route53

---

## Policy API

路由调度策略管理 API。

### Policy 端点列表

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/v1/policies` | 获取所有 Policy |
| POST | `/api/v1/policies` | 创建新的 Policy |
| GET | `/api/v1/policies/{name}` | 获取指定 Policy |
| PUT | `/api/v1/policies/{name}` | 更新指定 Policy |
| DELETE | `/api/v1/policies/{name}` | 删除指定 Policy |

### Policy 数据结构

```json
{
  "metadata": {
    "name": "failover-policy",
    "namespace": "default",
    "labels": {
      "env": "production",
      "type": "failover"
    }
  },
  "spec": {
    "name": "故障转移策略",
    "description": "主 CDN 故障时自动切换到备用 CDN",
    "type": "Failover",
    "enabled": true,
    "priority": 100,
    "rules": [
      {
        "id": "failover-rule-1",
        "condition": {
          "type": "HealthStatus",
          "operator": "EQUALS",
          "value": "unhealthy"
        },
        "action": {
          "type": "Route",
          "target": {
            "kind": "Distribution",
            "name": "backup-cdn",
            "namespace": "default"
          },
          "parameters": {
            "reason": "primary_cdn_unhealthy"
          }
        },
        "weight": 100,
        "priority": 1
      }
    ]
  },
  "status": {
    "phase": "Active",
    "message": "Policy is active and monitoring",
    "lastSyncTime": "2024-01-01T12:00:00Z",
    "executionCount": 0,
    "successCount": 0,
    "failureCount": 0
  }
}
```

### 支持的策略类型

- `LoadBalancing` - 负载均衡策略
- `Failover` - 故障转移策略
- `GeoRouting` - 地理路由策略
- `HealthCheck` - 健康检查策略
- `RateLimit` - 限流策略
- `Scheduled` - 定时策略
- `Canary` - 灰度发布策略

### 支持的条件类型

- `GeoLocation` - 地理位置
- `UserAgent` - 用户代理
- `RequestPath` - 请求路径
- `RequestHeader` - 请求头
- `SourceIP` - 源 IP
- `Time` - 时间条件
- `HealthStatus` - 健康状态
- `RequestRate` - 请求频率

### 支持的操作符

- `EQUALS` - 等于
- `NOT_EQUALS` - 不等于
- `CONTAINS` - 包含
- `NOT_CONTAINS` - 不包含
- `STARTS_WITH` - 以...开始
- `ENDS_WITH` - 以...结束
- `IN` - 在列表中
- `NOT_IN` - 不在列表中
- `GREATER_THAN` - 大于
- `LESS_THAN` - 小于
- `REGEX` - 正则匹配

### 支持的动作类型

- `Route` - 路由到指定资源
- `Redirect` - 重定向
- `Block` - 阻止访问
- `RateLimit` - 限制频率
- `SetHeader` - 设置请求头
- `RemoveHeader` - 移除请求头
- `ModifyResponse` - 修改响应
- `Log` - 记录日志

---

## 使用示例

### 创建 Distribution

```bash
curl -X POST http://localhost:8080/api/v1/distributions \
  -H "Content-Type: application/json" \
  -d '{
    "metadata": {
      "name": "my-cdn",
      "labels": {
        "env": "production"
      }
    },
    "spec": {
      "domain": "cdn.mysite.com",
      "vendor": "aws_cdn",
      "enabled": true,
      "origins": [
        {
          "id": "origin-1",
          "domainName": "origin.mysite.com",
          "originProtocolPolicy": "https-only"
        }
      ]
    }
  }'
```

### 创建故障转移策略

```bash
curl -X POST http://localhost:8080/api/v1/policies \
  -H "Content-Type: application/json" \
  -d '{
    "metadata": {
      "name": "failover-policy"
    },
    "spec": {
      "name": "故障转移策略",
      "type": "Failover",
      "enabled": true,
      "rules": [
        {
          "id": "rule-1",
          "condition": {
            "type": "HealthStatus",
            "operator": "EQUALS",
            "value": "unhealthy"
          },
          "action": {
            "type": "Route",
            "target": {
              "kind": "Distribution",
              "name": "backup-cdn"
            }
          }
        }
      ]
    }
  }'
```

### 创建定时策略

```bash
curl -X POST http://localhost:8080/api/v1/policies \
  -H "Content-Type: application/json" \
  -d '{
    "metadata": {
      "name": "scheduled-policy"
    },
    "spec": {
      "name": "定时切换策略",
      "type": "Scheduled",
      "enabled": true,
      "schedule": {
        "cronExpression": "0 0 6 * * *",
        "timezone": "Asia/Shanghai",
        "repeat": true
      },
      "rules": [
        {
          "id": "morning-switch",
          "condition": {
            "type": "Time",
            "operator": "EQUALS",
            "value": "06:00"
          },
          "action": {
            "type": "Route",
            "target": {
              "kind": "Distribution",
              "name": "morning-cdn"
            }
          }
        }
      ]
    }
  }'
```

---

## 错误处理

API 使用标准的 HTTP 状态码和 JSON 格式的错误响应。

### 错误响应格式

```json
{
  "error": "error_code",
  "message": "详细错误信息"
}
```

### 常见错误码

| HTTP 状态码 | 错误码 | 描述 |
|------------|--------|------|
| 400 | `invalid_request` | 请求格式错误 |
| 400 | `validation_failed` | 数据验证失败 |
| 404 | `not_found` | 资源不存在 |
| 409 | `conflict` | 资源冲突 |
| 500 | `internal_error` | 内部服务器错误 |
| 500 | `storage_error` | 存储错误 |

### 错误示例

```json
{
  "error": "validation_failed",
  "message": "资源名称不能为空"
}
```

---

## 开发环境

### 前置要求

- Go 1.24.6+
- Docker & Docker Compose
- Task (可选，用于构建)

### 启动开发环境

1. **启动 etcd 服务**

   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

2. **复制配置文件**

   ```bash
   cp config.example.yaml config.yaml
   ```

3. **安装依赖**

   ```bash
   go mod download
   ```

### 构建和运行

使用 Task (推荐):

```bash
# 查看所有可用任务
task --list

# 开发模式运行
task dev

# 构建并运行
task run

# 构建优化版本
task build

# 代码格式化
task format

# 代码检查
task lint

# 运行测试
task test
```

使用 Go 命令:

```bash
# 构建
go build -o mcdn cmd/mcdn/main.go

# 运行
./mcdn --help
```

### 启动 API 服务器

```bash
# 使用默认配置启动
./mcdn apiserver

# 指定绑定地址
./mcdn apiserver --bind :9090

# 指定配置文件
./mcdn --config config.yaml apiserver
```

### 测试 API

```bash
# 健康检查
curl http://localhost:8080/health

# 创建测试策略
curl -X POST http://localhost:8080/api/v1/policies \
  -H "Content-Type: application/json" \
  -d @test-policy.json

# 查看所有策略
curl http://localhost:8080/api/v1/policies
```

---

## 配置文件

参考 [`config.example.yaml`](config.example.yaml) 创建配置文件：

```yaml
env: dev
log:
  level: info
  path: ./log/app.log
worker:
  concurrency: 4
  queue: default
etcd:
  endpoints:
    - localhost:2379
  timeout: 5s
```

配置文件支持 JSON Schema 验证，详见 [`config.schema.json`](config.schema.json)。

---

## 项目结构

```bash
mcdn/
├── README.md                    # 项目说明
├── go.mod                       # Go 模块定义
├── go.sum                       # 依赖校验文件
├── config.example.yaml          # 配置文件示例
├── config.schema.json           # 配置文件 JSON Schema
├── docker-compose.dev.yml       # 开发环境 Docker Compose
├── Taskfile.yml                 # Task 构建配置
├── api/                        # API 定义
│   └── v1/                     # v1 版本 API
│       ├── distribution.go     # CDN 分发资源定义
│       ├── dns.go             # DNS 资源定义
│       ├── policy.go           # 路由资源定义
│       ├── resource.go         # 资源接口定义
│       └── types.go           # 通用类型定义
├── cmd/                        # 命令行入口
│   └── mcdn/
│       └── main.go            # 主程序入口
├── internal/                   # 内部包
│   ├── apiserver/             # API 服务器
│   │   └── server.go          # HTTP 服务器实现
│   ├── app/                   # 应用框架
│   │   ├── app.go            # CLI 应用定义
│   │   └── commands/         # 命令实现
│   ├── controller/            # 控制器框架
│   ├── logger/               # 日志组件
│   ├── resource/             # 资源控制器
│   └── vendor/               # 厂商适配器
├── pkg/                        # 公共包
│   ├── config/               # 配置管理
│   └── worker/               # 任务执行器
└── examples/                   # 示例文件
    └── policy-scheduled.yaml  # 定时策略示例
```

---

## 功能特性

### 资源管理

- **CDN Distribution**: 管理多云厂商的 CDN 分发配置
- **DNS Zone/Record**: 管理 DNS 解析配置和记录
- **Policy**: 管理智能路由调度策略

### 调度策略

- **负载均衡**: 基于权重的流量分发
- **故障转移**: 自动故障检测和切换
- **地理路由**: 基于地理位置的智能调度
- **健康检查**: 实时监控服务健康状态
- **限流控制**: 请求频率限制和保护
- **灰度发布**: 金丝雀部署支持
- **定时调度**: 基于 Cron 表达式的定时任务

### 多云支持

- **CDN 厂商**: AWS CloudFront, GCP Media CDN, 阿里云 CDN, Akamai
- **DNS 厂商**: 腾讯云 DNSPod, 阿里云 DNS, AWS Route53
- **统一 API**: 屏蔽厂商差异，提供统一的管理接口

---

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t mcdn .

# 运行容器
docker run -d \
  --name mcdn \
  -p 8080:8080 \
  -v /path/to/config.yaml:/app/config.yaml \
  mcdn apiserver
```

### 生产环境

1. 使用 `task build` 构建优化版本
2. 配置生产环境的 etcd 集群
3. 调整配置文件中的日志级别和并发数
4. 设置适当的监控和告警

---

## 监控和日志

- 日志输出支持结构化格式
- 支持多种日志级别 (trace, debug, info, warn, error, fatal, panic)
- 内置性能指标收集
- 支持分布式链路追踪

---

## 开发指南

### 添加新的资源控制器

1. 在 `api/v1/` 中定义资源类型
2. 在 `internal/resource/` 中实现控制器
3. 在控制器管理器中注册新控制器
4. 添加相应的测试用例

### 代码规范

- 遵循 Go 官方代码规范
- 使用 `goimports` 和 `gofumpt` 格式化代码
- 通过 `golangci-lint` 代码检查
- 编写单元测试和集成测试

---

## 许可证

本项目为内部项目，版权归 Lilith Games 所有。
