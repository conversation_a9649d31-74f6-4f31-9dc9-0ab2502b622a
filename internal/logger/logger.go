package logger

import (
	"log/slog"
	"os"
)

// Logger 定义日志接口
type Logger interface {
	Debug(msg string, keysAndValues ...any)
	Info(msg string, keysAndValues ...any)
	Warn(msg string, keysAndValues ...any)
	Error(msg string, keysAndValues ...any)
}

// SlogLogger 基于 slog 的日志实现
type SlogLogger struct {
	logger *slog.Logger
}

// NewLogger 创建新的日志记录器
func NewLogger() Logger {
	// 创建带有自定义前缀的 TextHandler
	opts := &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}
	handler := slog.NewTextHandler(os.Stdout, opts)

	return &SlogLogger{
		logger: slog.New(handler),
	}
}

// NewLoggerWithLevel 创建指定日志级别的日志记录器
func NewLoggerWithLevel(level slog.Level) Logger {
	opts := &slog.HandlerOptions{
		Level: level,
	}
	handler := slog.NewTextHandler(os.Stdout, opts)

	return &SlogLogger{
		logger: slog.New(handler).With("service", "mcdn"),
	}
}

// NewJSONLogger 创建 JSON 格式的日志记录器
func NewJSONLogger() Logger {
	opts := &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}
	handler := slog.NewJSONHandler(os.Stdout, opts)

	return &SlogLogger{
		logger: slog.New(handler).With("service", "mcdn"),
	}
}

// Debug 记录调试信息
func (l *SlogLogger) Debug(msg string, keysAndValues ...any) {
	l.logger.Debug(msg, keysAndValues...)
}

// Info 记录信息
func (l *SlogLogger) Info(msg string, keysAndValues ...any) {
	l.logger.Info(msg, keysAndValues...)
}

// Warn 记录警告
func (l *SlogLogger) Warn(msg string, keysAndValues ...any) {
	l.logger.Warn(msg, keysAndValues...)
}

// Error 记录错误
func (l *SlogLogger) Error(msg string, keysAndValues ...any) {
	l.logger.Error(msg, keysAndValues...)
}
