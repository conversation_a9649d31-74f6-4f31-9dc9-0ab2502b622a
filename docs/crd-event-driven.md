# MCDN CRD 事件驱动架构

## 架构概述

MCDN 系统是基于 k8s-arch-like CRD 事件驱动模式设计，包含以下核心组件：

### 1. CRD 资源定义

- `Route` - 路由策略资源 (支持 Cron 调度)
- `Distribution` - CDN 分发资源
- `DNSZone` - DNS 区域资源
- `DNSRecord` - DNS 记录资源

### 2. 控制器架构

```text
internal/controller/
├── controller.go    # 基础控制器接口和实现
├── scheduler.go     # Cron 调度器 (通用组件)
├── registry.go      # 控制器注册管理
└── watch.go         # etcd 事件监听

internal/resource/
├── route/           # Route 控制器
├── distribution/    # Distribution 控制器  
└── dns/            # DNS 控制器
```

## 事件驱动流程

### 1. CRD 资源创建

```bash
# 用户创建 Route 资源
mcdnctl apply -f route-scheduled.yaml
# 或通过 API/etcd 直接写入
```

### 2. Controller 监听

```go
// 控制器监听 etcd 路径: /mcdn/policy/
watcher := NewWatcher(etcdClient, "/mcdn/policy/", logger)
eventCh, err := watcher.Start(ctx)

// 处理事件
for event := range eventCh {
    controller.Reconcile(ctx, event.Key, event.Value)
}
```

### 3. Reconcile 协调

```go
func (c *Controller) Reconcile(ctx context.Context, key string, obj any) error {
    // 解析资源
    route := parseRoute(obj)
    
    // 协调期望状态与实际状态
    return c.reconcileRoute(ctx, route)
}
```

### 4. 状态更新

```go
// 更新资源状态到 etcd
route.Status.Phase = RoutePhaseActive
route.Status.Message = "定时任务已激活"
etcdClient.Put(ctx, key, marshalRoute(route))
```

## 完整示例：基于 Cron 的 CDN 切换

### 1. Route CRD 定义

```yaml
apiVersion: mcdn.lilithgames.com/v1
kind: Route
metadata:
  name: daily-cdn-switch
  namespace: default
spec:
  type: Scheduled
  enabled: true
  schedule:
    cronExpression: "0 0 6 * * *"  # 每天 6:00 AM
    timezone: "Asia/Shanghai"
  rules:
    - id: "switch-to-b-cdn"
      action:
        type: Route
        target:
          kind: Distribution
          name: b-cdn-distribution
```

### 2. 事件处理流程

#### 2.1 创建事件 (PUT /mcdn/policy/default/daily-cdn-switch)

```go
func (c *Controller) Reconcile(ctx context.Context, key string, obj any) error {
    route := parseRoute(obj) // 解析 Route 资源
    
    if route.Status.Phase == "" {
        // 新创建的资源，设置为 Pending 状态
        return c.activateRoute(ctx, route)
    }
    
    return c.reconcileRoute(ctx, route)
}
```

#### 2.2 激活路由 (对于 Scheduled 类型)

```go
func (c *Controller) activateRoute(ctx context.Context, route *Route) error {
    if route.Spec.Type == RouteTypeScheduled {
        // 创建 Cron 任务
        task := &RouteTask{
            route:      route,
            controller: c,
        }
        
        // 添加到调度器
        c.scheduler.AddTask(task)
        
        // 更新状态
        route.Status.Phase = RoutePhaseActive
        route.Status.Message = "定时策略已激活"
        
        return c.updateRouteStatus(ctx, route)
    }
}
```

#### 2.3 Cron 任务执行

```go
func (t *RouteTask) Execute(ctx context.Context) error {
    // 每天 6:00 AM 自动执行
    for _, rule := range t.route.Spec.Rules {
        if rule.Action.Type == ActionTypeRoute {
            // 执行 CDN 切换
            err := t.controller.executeRouteAction(ctx, t.route, rule)
            if err != nil {
                return err
            }
        }
    }
    
    // 更新执行统计
    t.route.Status.ExecutionCount++
    t.route.Status.LastExecutionTime = &now
    
    return t.controller.updateRouteStatus(ctx, t.route, RoutePhaseActive, "定时任务执行成功")
}
```

#### 2.4 更新事件处理

```go
// 用户修改 Route 资源 (比如更改 Cron 表达式)
func (c *Controller) reconcileRoute(ctx context.Context, route *Route) error {
    switch route.Status.Phase {
    case RoutePhaseActive:
        return c.handleActiveRoute(ctx, route)
    }
}

func (c *Controller) handleActiveRoute(ctx context.Context, route *Route) error {
    if route.Spec.Type == RouteTypeScheduled {
        // 同步调度器中的任务
        return c.syncScheduledRoute(ctx, route)
    }
}

func (c *Controller) syncScheduledRoute(ctx context.Context, route *Route) error {
    taskName := fmt.Sprintf("route-task-%s", route.Metadata.Name)
    
    if wrapper, exists := c.scheduler.GetTask(taskName); exists {
        // 检查 Cron 表达式是否变更
        if wrapper.Task.GetSchedule() != route.Spec.Schedule.CronExpression {
            // 更新任务
            newTask := &RouteTask{route: route, controller: c}
            return c.scheduler.UpdateTask(newTask)
        }
    }
}
```

#### 2.5 删除事件处理

```go
// DELETE /mcdn/policy/default/daily-cdn-switch (obj = nil/empty)
func (c *Controller) Reconcile(ctx context.Context, key string, obj any) error {
    if len(obj.([]byte)) == 0 {
        // 资源被删除
        return c.handleRouteDelete(ctx, key)
    }
}

func (c *Controller) handleRouteDelete(ctx context.Context, key string) error {
    routeName := extractRouteNameFromKey(key)
    taskName := fmt.Sprintf("route-task-%s", routeName)
    
    // 从调度器中移除任务
    c.scheduler.RemoveTask(taskName)
    
    return nil
}
```

## 关键特性

### 1. 声明式 API

- 用户声明期望状态 (`spec`)
- 控制器维护实际状态 (`status`)
- 自动协调差异

### 2. 事件驱动

- etcd Watch 机制监听资源变更
- 增量处理，只处理变更的资源
- 支持 CREATE/UPDATE/DELETE 事件

### 3. 状态管理

```go
type RoutePhase string

const (
    RoutePhasePending   RoutePhase = "Pending"    // 待激活
    RoutePhaseActive    RoutePhase = "Active"     // 活跃
    RoutePhaseInactive  RoutePhase = "Inactive"   // 非活跃
    RoutePhaseUpdating  RoutePhase = "Updating"   // 更新中
    RoutePhaseDeleting  RoutePhase = "Deleting"   // 删除中
    RoutePhaseFailed    RoutePhase = "Failed"     // 失败
)
```

### 4. 幂等性

- Reconcile 方法可重复调用
- 根据当前状态和期望状态进行协调
- 支持错误重试和状态恢复

### 5. 扩展性

- 插件化的控制器架构
- CronScheduler 可复用于其他资源
- 支持多种资源类型和厂商

## 运维和监控

### 1. 状态查询

```bash
# 查看 Route 资源状态
etcdctl get --prefix /mcdn/policy/

# 查看调度器统计
curl http://mcdn-controller:8080/api/v1/scheduler/stats
```

### 2. 日志监控

```bash
# 控制器日志(必须设置 MCDN_API_TOKEN=XXXX)
mcdnctl logs mcdn-controller -f

# 关键事件
INFO 收到资源变更事件 type=PUT key=/mcdn/policy/default/daily-cdn-switch
INFO 开始协调路由策略资源 key=/mcdn/policy/default/daily-cdn-switch
INFO 成功添加调度任务 task=route-task-daily-cdn-switch schedule="0 0 6 * * *"
INFO 执行路由调度任务 route=daily-cdn-switch runCount=1
```

### 3. 指标收集

- 控制器协调次数
- Cron 任务执行统计
- 错误率和延迟
- 资源状态分布
