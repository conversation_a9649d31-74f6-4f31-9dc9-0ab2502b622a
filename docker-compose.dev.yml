services:
  etcd:
    image: bitnami/etcd:3.5
    container_name: etcd-dev
    restart: unless-stopped
    ports:
      - "2379:2379" # Client port
      - "2380:2380" # Peer port
    environment:
      # 允许无认证访问（仅用于开发环境）
      ALLOW_NONE_AUTHENTICATION: "yes"

      # Client 配置
      ETCD_ADVERTISE_CLIENT_URLS: "http://0.0.0.0:2379"
      ETCD_LISTEN_CLIENT_URLS: "http://0.0.0.0:2379"

      # Peer 配置
      ETCD_LISTEN_PEER_URLS: "http://0.0.0.0:2380"
      ETCD_INITIAL_ADVERTISE_PEER_URLS: "http://0.0.0.0:2380"

      # 集群配置
      ETCD_NAME: "node1"
      ETCD_INITIAL_CLUSTER: "node1=http://0.0.0.0:2380"
      ETCD_INITIAL_CLUSTER_TOKEN: "etcd-cluster"
      ETCD_INITIAL_CLUSTER_STATE: "new"

      # 调试和开发配置
      ETCD_LOG_LEVEL: "info"
      ETCD_AUTO_COMPACTION_MODE: "periodic"
      ETCD_AUTO_COMPACTION_RETENTION: "1"
    volumes:
      # 持久化数据（可选，注释掉则每次重启都是全新的）
      - etcd_data:/bitnami/etcd/data
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    networks:
      - dev-network

volumes:
  etcd_data:
    driver: local

networks:
  dev-network:
    driver: bridge
