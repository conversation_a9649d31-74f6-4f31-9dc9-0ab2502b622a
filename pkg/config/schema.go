package config

import (
	"encoding/json"
	"time"
)

// Config 主配置结构（导出供其它包使用）
type Config struct {
	Env        string           `mapstructure:"env" json:"env"`
	Log        LogConfig        `mapstructure:"log" json:"log"`
	API        APIConfig        `mapstructure:"api" json:"api"`
	Controller ControllerConfig `mapstructure:"controller" json:"controller"`
	Worker     WorkerConfig     `mapstructure:"worker" json:"worker"`
	Etcd       EtcdConfig       `mapstructure:"etcd" json:"etcd"`
}

type LogConfig struct {
	Path  string `mapstructure:"path" json:"path"`
	Level string `mapstructure:"level" json:"level"`
}

type APIConfig struct {
	Enabled bool   `mapstructure:"enabled" json:"enabled"`
	Host    string `mapstructure:"host" json:"host"`
	Port    int    `mapstructure:"port" json:"port"`
}

type ControllerConfig struct {
	Enabled bool `mapstructure:"enabled" json:"enabled"`
	Workers int  `mapstructure:"workers" json:"workers"`
}

type WorkerConfig struct {
	Concurrency int    `mapstructure:"concurrency" json:"concurrency"`
	Queue       string `mapstructure:"queue" json:"queue"`
}

type EtcdConfig struct {
	Endpoints []string      `mapstructure:"endpoints" json:"endpoints"`
	Timeout   time.Duration `mapstructure:"timeout" json:"timeout"`
}

func (c *Config) String() string {
	bytes, _ := json.MarshalIndent(c, "", "  ")
	return string(bytes)
}
