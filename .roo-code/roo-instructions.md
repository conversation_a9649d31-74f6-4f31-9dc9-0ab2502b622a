# MCDN 项目 Roo-Code 主配置

## 项目概述

**项目名称**: MCDN  
**项目类型**: Go 语言微服务  
**主要功能**: 多云CDN调度管理平台，基于 etcd 实现资源监听和控制器模式，提供完整的 CDN 分发、DNS 解析和路由调度管理能力  
**技术栈**: Go 1.24.6, etcd, Task, golangci-lint  

## 核心指导原则

### 1. 代码风格 (Google Go Style)

- **严格遵循** Google Go 代码风格指南
- **行长度限制**: 120 字符
- **格式化工具**: gofumpt + goimports
- **代码检查**: golangci-lint 配置见 `.golangci.yml`

```go
// 推荐的代码风格示例
func (c *Controller) handleResource(ctx context.Context, key string) error {
    c.logger.Info("处理资源", "key", key, "type", c.resourceType)
    
    if err := c.validateResource(key); err != nil {
        return fmt.Errorf("验证资源失败: %w", err)
    }
    
    return nil
}
```

### 2. 项目架构

**架构模式**: 控制器模式 + 观察者模式  
**数据流**: `etcd 变更 → Watcher → Controller → 业务逻辑 → 云厂商 API → 状态更新`

**目录结构**:

```
├── api/v1/              # API 类型定义
├── cmd/                 # 应用程序入口
├── internal/            # 内部实现
│   ├── controller/      # 控制器框架
│   ├── logger/          # 日志组件
│   └── resource/        # 资源控制器
├── pkg/                 # 公共包
└── vendor/              # 云厂商客户端
```

### 3. 开发规范

#### 接口设计

```go
type Controller interface {
    GetName() string
    GetResourceType() string
    Start(ctx context.Context) error
    Stop() error
    Reconcile(ctx context.Context, key string, obj any) error
}
```

#### 错误处理

- 使用 `fmt.Errorf` 包装错误
- 提供足够的上下文信息
- 遵循 Go 1.13+ 错误处理最佳实践

#### 日志记录

```go
// 使用结构化日志
c.logger.Info("处理资源", "key", key, "phase", phase)
c.logger.Error("处理失败", "key", key, "error", err)
```

## 技术配置

### Go 版本和依赖

- **Go 版本**: 1.24.6
- **核心依赖**:
  - `go.etcd.io/etcd/client/v3` - etcd 客户端
  - `github.com/spf13/viper` - 配置管理
  - `github.com/urfave/cli/v2` - CLI 框架
  - `github.com/gin-gonic/gin` - HTTP 框架

### 构建配置

**主要命令**:

```bash
task dev      # 开发模式运行
task build    # 构建二进制文件
task format   # 代码格式化
task lint     # 代码检查
task test     # 运行测试
task all      # 完整构建流程
```

**构建优化**:

- `CGO_ENABLED=0` - 静态链接
- `-ldflags="-s -w"` - 去除调试信息
- `-trimpath` - 移除路径信息

## AI 助手指导规则

### 代码修改时必须遵循

1. **代码风格检查**
   - 运行 `task format` 格式化代码
   - 运行 `task lint` 检查代码质量
   - 确保通过所有 linter 检查

2. **架构一致性**
   - 新增资源类型必须实现 `Controller` 接口
   - 遵循现有的目录结构和命名规范
   - 保持与现有代码的一致性

3. **错误处理**
   - 所有错误必须正确处理，不能忽略
   - 使用 `fmt.Errorf` 包装错误并提供上下文
   - 关键操作必须记录日志

4. **测试要求**
   - 新增功能必须包含单元测试
   - 运行 `task test` 确保所有测试通过
   - 测试覆盖率应保持在合理水平

### 文件修改指南

#### API 类型定义 (`api/v1/`)

- 新增类型必须包含完整的 JSON 标签
- 遵循现有的命名规范
- 包含必要的验证逻辑

#### 控制器实现 (`internal/resource/`)

- 实现完整的 `Controller` 接口
- 包含资源验证逻辑
- 实现状态管理和错误处理

#### 配置文件

- 修改 `Taskfile.yml` 时保持现有变量命名
- 更新 `.golangci.yml` 时确保不破坏现有检查
- 配置变更需要更新相关文档

### 常见任务处理

#### 添加新资源类型

1. 在 `api/v1/` 定义资源类型
2. 在 `internal/resource/` 创建控制器
3. 在 `registry.go` 注册控制器
4. 添加相应的测试

#### 修复 Bug

1. 先写测试重现问题
2. 修复代码
3. 确保测试通过
4. 运行完整的检查流程

#### 性能优化

1. 使用基准测试验证改进
2. 保持 API 兼容性
3. 更新相关文档

### 禁止的操作

1. **不要**忽略错误处理
2. **不要**破坏现有的 API 兼容性
3. **不要**绕过代码检查工具
4. **不要**提交未格式化的代码
5. **不要**删除现有的测试

### 推荐的工作流程

1. **理解需求** - 仔细分析要实现的功能
2. **查看现有代码** - 了解相似功能的实现方式
3. **设计方案** - 确保符合项目架构
4. **编写代码** - 遵循代码风格和最佳实践
5. **测试验证** - 运行 `task all` 确保质量
6. **文档更新** - 更新相关文档

## 参考文档

- [代码风格详细说明](./code-style.md)
- [项目结构说明](./project-structure.md)
- [Go 配置和依赖](./golang-config.md)
- [构建配置说明](./build-config.md)

## 联系信息

如有疑问，请参考项目 README.md 或相关文档。
