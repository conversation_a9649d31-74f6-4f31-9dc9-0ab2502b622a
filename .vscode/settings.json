{"go.lintTool": "golangci-lint", "go.lintFlags": ["--fast"], "go.formatTool": "gofmt", "[go]": {"editor.codeActionsOnSave": {"source.organizeImports": "always"}}, "yaml.schemas": {"https://golangci-lint.run/jsonschema/golangci.v1.jsonschema.json": ".golangci.yml", "./config/schema.json": ["config.yaml", "config/config.yaml", "config.example.yaml"]}, "github.copilot.chat.commitMessageGeneration.instructions": [{"file": ".github/git-message.md"}]}