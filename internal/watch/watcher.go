package watch

import (
	"context"
	"fmt"

	clientv3 "go.etcd.io/etcd/client/v3"

	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/logger"
)

// EventType 定义事件类型
type EventType string

const (
	EventAdded    EventType = "ADDED"
	EventModified EventType = "MODIFIED"
	EventDeleted  EventType = "DELETED"
)

// Event 表示资源变更事件
type Event struct {
	Type  EventType
	Key   string
	Value []byte
}

// Watcher 定义了资源监听器接口
type Watcher interface {
	// Start 启动监听，返回事件通道
	Start(ctx context.Context) (<-chan *Event, error)
	// Stop 停止监听
	Stop() error
}

// etcdWatcher 实现了基于 etcd 的资源监听
type etcdWatcher struct {
	client    *clientv3.Client
	prefix    string
	logger    logger.Logger
	eventChan chan *Event
	stopCh    chan struct{}
}

// NewWatcher 创建新的 etcd 资源监听器
func NewWatcher(client *clientv3.Client, prefix string, log logger.Logger) Watcher {
	return &etcdWatcher{
		client:    client,
		prefix:    prefix,
		logger:    log,
		eventChan: make(chan *Event),
		stopCh:    make(chan struct{}),
	}
}

// Start 启动监听
func (w *etcdWatcher) Start(ctx context.Context) (<-chan *Event, error) {
	// 先执行一次 List 操作
	resp, err := w.client.Get(ctx, w.prefix, clientv3.WithPrefix())
	if err != nil {
		return nil, fmt.Errorf("list resources failed: %w", err)
	}

	// 发送现有资源的 ADDED 事件
	go func() {
		for _, kv := range resp.Kvs {
			w.eventChan <- &Event{
				Type:  EventAdded,
				Key:   string(kv.Key),
				Value: kv.Value,
			}
		}

		// 开始 Watch
		watchChan := w.client.Watch(ctx, w.prefix, clientv3.WithPrefix(), clientv3.WithPrevKV())
		for {
			select {
			case <-ctx.Done():
				close(w.eventChan)
				return
			case <-w.stopCh:
				close(w.eventChan)
				return
			case watchResp := <-watchChan:
				if watchResp.Err() != nil {
					w.logger.Error("watch error", "error", watchResp.Err())
					continue
				}

				for _, event := range watchResp.Events {
					var eventType EventType
					switch event.Type {
					case clientv3.EventTypePut:
						if event.PrevKv == nil {
							eventType = EventAdded
						} else {
							eventType = EventModified
						}
					case clientv3.EventTypeDelete:
						eventType = EventDeleted
					}

					w.eventChan <- &Event{
						Type:  eventType,
						Key:   string(event.Kv.Key),
						Value: event.Kv.Value,
					}
				}
			}
		}
	}()

	return w.eventChan, nil
}

// Stop 停止监听
func (w *etcdWatcher) Stop() error {
	close(w.stopCh)
	return nil
}
