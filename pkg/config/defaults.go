package config

import "time"

var defaultConfig = Config{
	Env: "dev",
	Log: LogConfig{
		Path:  "",
		Level: "info",
	},
	API: APIConfig{
		Enabled: true,
		Host:    "0.0.0.0",
		Port:    8080,
	},
	Controller: ControllerConfig{
		Enabled: true,
		Workers: 2,
	},
	Worker: WorkerConfig{
		Concurrency: 4,
		Queue:       "default",
	},
	Etcd: EtcdConfig{
		Endpoints: []string{"127.0.0.1:2379"},
		Timeout:   5 * time.Second,
	},
}
