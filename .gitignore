# ---- Go build artifacts ----
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out

# Built application binaries (adjust if you output elsewhere)
/mcdn-worker
/bin/
/dist/
/build/

# Go workspaces / modules
# vendor/
# !pkg/vendor
# Uncomment below if you use go workspaces with temporary modules
# go.work
# go.work.sum

# Code coverage & profiling
*.coverprofile
*.cov
coverage.*
cover.out
profile.out
cpu.prof
mem.prof

# Logs & temp files
*.log
*.tmp
*.swp
*~

# IDE / editor configs (keep project-shared ones selectively)
.idea/
# 先忽略 .vscode 下所有文件，再通过否定规则放行需要共享的文件
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/extensions.json

# OS files
.DS_Store

# ---- Project specific configuration ----
# The real runtime configuration should not be committed.
# Commit the example file instead (see config.example.yaml) and copy / rename locally.
/config.yaml
config/config.yaml
# You can maintain local overrides like config.local.yaml as needed
config.local.yaml

# ---- Misc ----
# Dependency scanners or tooling caches (uncomment if needed)
# .scan/
