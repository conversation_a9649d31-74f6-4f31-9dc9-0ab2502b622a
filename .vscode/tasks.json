{"version": "2.0.0", "tasks": [{"label": "go: install tools", "type": "shell", "command": "bash", "args": ["-c", "set -euo pipefail; echo 'Installing Go tools...'; export CGO_ENABLED=0; go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest; go install golang.org/x/tools/cmd/goimports@latest; echo 'Done.'"], "problemMatcher": []}, {"label": "go: tidy", "type": "shell", "command": "go", "args": ["mod", "tidy"], "problemMatcher": ["$go"], "group": "none"}, {"label": "go: lint", "type": "shell", "command": "golangci-lint", "args": ["run", "--timeout=3m", "./..."], "problemMatcher": ["$go"], "dependsOn": ["go: install tools"], "group": "test"}, {"label": "go: format (imports + gofumpt)", "type": "shell", "command": "bash", "args": ["-c", "set -euo pipefail; echo 'Running goimports ...'; goimports -w .; if command -v gofumpt >/dev/null 2>&1; then echo 'Running gofumpt ...'; gofumpt -l -w .; fi"], "problemMatcher": []}, {"label": "go: build mcdn", "type": "shell", "command": "go", "args": ["build", "-o", "bin/mcdn", "./cmd/mcdn"], "options": {"env": {"CGO_ENABLED": "0"}}, "problemMatcher": ["$go"], "group": {"kind": "build", "isDefault": true}, "dependsOn": ["go: install tools"]}, {"label": "go: run", "type": "shell", "command": "go", "args": ["run", "./cmd/mcdn", "-config", "config/config.yaml"], "problemMatcher": ["$go"], "dependsOn": ["go: install tools"]}]}