apiVersion: mcdn.lilithgames.com/v1
kind: Policy
metadata:
  name: daily-cdn-switch
  namespace: default
  labels:
    app: mcdn
    type: scheduled
spec:
  name: "每日 CDN 切换策略"
  description: "每天凌晨 6-8 点切换到 B CDN，其他时间使用 A CDN"
  type: Scheduled
  enabled: true
  priority: 100
  
  # 调度配置
  schedule:
    # Cron 表达式：每天 6:00 AM 执行 (切换到 B CDN)
    cronExpression: "0 0 6 * * *"
    timezone: "Asia/Shanghai"
    repeat: true
  
  # 策略规则
  rules:
    - id: "switch-to-b-cdn"
      priority: 1
      condition:
        type: Time
        operator: EQUALS
        value: "06:00"
      action:
        type: Policy
        target:
          kind: Distribution
          name: b-cdn-distribution
        parameters:
          reason: "切换到 B CDN (6:00 AM)"
          
---
apiVersion: mcdn.lilithgames.com/v1
kind: Policy
metadata:
  name: daily-cdn-switch-back
  namespace: default
  labels:
    app: mcdn
    type: scheduled
spec:
  name: "每日 CDN 切换回 A 策略"
  description: "每天上午 8 点切换回 A CDN"
  type: Scheduled
  enabled: true
  priority: 100
  
  # 调度配置
  schedule:
    # Cron 表达式：每天 8:00 AM 执行 (切换回 A CDN)
    cronExpression: "0 0 8 * * *"
    timezone: "Asia/Shanghai"
    repeat: true
  
  # 策略规则
  rules:
    - id: "switch-to-a-cdn"
      priority: 1
      condition:
        type: Time
        operator: EQUALS
        value: "08:00"
      action:
        type: Policy
        target:
          kind: Distribution
          name: a-cdn-distribution
        parameters:
          reason: "切换回 A CDN (8:00 AM)"

---
# 更复杂的调度示例：工作日和周末使用不同的 CDN
apiVersion: mcdn.lilithgames.com/v1
kind: Policy
metadata:
  name: weekday-weekend-cdn-switch
  namespace: default
  labels:
    app: mcdn
    type: scheduled
spec:
  name: "工作日/周末 CDN 切换策略"
  description: "工作日使用高性能 CDN，周末使用经济型 CDN"
  type: Scheduled
  enabled: true
  priority: 200
  
  # 调度配置 - 每周一 9:00 AM 切换到高性能 CDN
  schedule:
    cronExpression: "0 0 9 * * 1"  # 每周一 9:00 AM
    timezone: "Asia/Shanghai"
    repeat: true
  
  rules:
    - id: "switch-to-high-performance-cdn"
      priority: 1
      condition:
        type: Time
        operator: EQUALS
        value: "weekday"
      action:
        type: Policy
        target:
          kind: Distribution
          name: high-performance-cdn
        parameters:
          reason: "工作日切换到高性能 CDN"

---
apiVersion: mcdn.lilithgames.com/v1
kind: Policy
metadata:
  name: weekend-cdn-switch
  namespace: default
  labels:
    app: mcdn
    type: scheduled
spec:
  name: "周末经济型 CDN 切换"
  description: "每周六切换到经济型 CDN"
  type: Scheduled
  enabled: true
  priority: 200
  
  # 调度配置 - 每周六 0:00 AM 切换到经济型 CDN
  schedule:
    cronExpression: "0 0 0 * * 6"  # 每周六 12:00 AM
    timezone: "Asia/Shanghai"
    repeat: true
  
  rules:
    - id: "switch-to-economic-cdn"
      priority: 1
      condition:
        type: Time
        operator: EQUALS
        value: "weekend"
      action:
        type: Policy
        target:
          kind: Distribution
          name: economic-cdn
        parameters:
          reason: "周末切换到经济型 CDN"

---
# 复合调度示例：基于时间和地理位置的复合策略
apiVersion: mcdn.lilithgames.com/v1
kind: Policy
metadata:
  name: peak-hours-geo-routing
  namespace: default
  labels:
    app: mcdn
    type: scheduled
spec:
  name: "高峰期地理路由策略"
  description: "高峰期（19-23点）根据地理位置智能路由"
  type: Scheduled
  enabled: true
  priority: 300
  
  # 调度配置 - 每天 19:00 开启地理智能路由
  schedule:
    cronExpression: "0 0 19 * * *"  # 每天 7:00 PM
    timezone: "Asia/Shanghai"
    repeat: true
  
  rules:
    - id: "enable-geo-routing-peak"
      priority: 1
      condition:
        type: Time
        operator: GREATER_THAN
        value: "19:00"
      action:
        type: Policy
        target:
          kind: Policy
          name: geo-intelligent-routing
        parameters:
          reason: "高峰期启用地理智能路由"
          duration: "4h"  # 持续4小时到23:00
