package app

import (
	"fmt"

	"github.com/urfave/cli/v2"

	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/app/commands"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/version"
)

// New returns a configured cli.App instance
func New() *cli.App {
	return &cli.App{
		Name:    "mcdn",
		Usage:   "MCDN worker 服务",
		Version: fmt.Sprintf("%s (commit:%s build:%s)", version.Version, version.Commit, version.BuildTime),
		Flags: []cli.Flag{
			&cli.StringFlag{
				Name:    "config",
				Aliases: []string{"c"},
				Value:   "config.yaml",
				Usage:   "配置文件路径",
				EnvVars: []string{"MCDN_CONFIG"},
			},
		},
		Commands: commands.Commands(),
		Action:   func(c *cli.Context) error { return cli.ShowAppHelp(c) },
	}
}

// Run executes the cli app with given args
func Run(args []string) error {
	return New().Run(args)
}
