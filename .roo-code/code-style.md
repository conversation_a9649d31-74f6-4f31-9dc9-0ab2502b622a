# 代码风格配置

## Google Go 代码风格

本项目严格遵循 Google Go 代码风格指南，具体配置如下：

### 格式化工具

- **gofumpt**: 更严格的 gofmt 替代品
- **goimports**: 自动管理导入语句
- **golangci-lint**: 代码质量检查

### 启用的 Linters

```yaml
linters:
  enable:
    - gofumpt      # 严格的代码格式化
    - gosimple     # 简化代码建议
    - gofmt        # 标准格式化
    - unused       # 检查未使用的变量/函数
    - errname      # 错误命名规范
    - gci          # 导入语句分组
    - lll          # 行长度限制
```

### 代码格式化规则

1. **行长度限制**: 120 字符
2. **接口类型**: 使用 `any` 替代 `interface{}`
3. **切片操作**: `a[b:]` 替代 `a[b:len(a)]`

### 导入语句分组

```go
import (
    // 标准库
    "context"
    "fmt"
    
    // 第三方库
    "github.com/spf13/viper"
    clientv3 "go.etcd.io/etcd/client/v3"
    
    // 项目内部包
    "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/api/v1"
    "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/controller"
)
```

### 命名规范

1. **包名**: 小写，简短，有意义
2. **函数名**: 驼峰命名，公开函数首字母大写
3. **变量名**: 驼峰命名，简洁明了
4. **常量名**: 全大写或驼峰命名
5. **接口名**: 以 -er 结尾（如 Controller, Logger）

### 注释规范

```go
// Controller 定义了资源控制器的接口
// 所有具体的资源控制器都应该实现这个接口
type Controller interface {
    // GetName 返回控制器名称
    GetName() string
    
    // Start 启动控制器
    // ctx: 上下文，用于控制生命周期
    Start(ctx context.Context) error
}
```

### 错误处理

```go
// 推荐的错误处理方式
func (c *Controller) handleResource(ctx context.Context, key string) error {
    if err := c.validateResource(key); err != nil {
        return fmt.Errorf("验证资源失败: %w", err)
    }
    
    // 业务逻辑...
    
    return nil
}
```

### 日志记录

```go
// 使用结构化日志
c.logger.Info("处理资源",
    "key", key,
    "type", resourceType,
    "phase", phase)

c.logger.Error("处理失败",
    "key", key,
    "error", err)
```

## 自动格式化命令

```bash
# 格式化代码
task format

# 或者手动执行
goimports -w .
gofumpt -l -w .
```

## 代码检查

```bash
# 运行所有检查
task lint

# 或者手动执行
golangci-lint run --timeout=3m ./...
