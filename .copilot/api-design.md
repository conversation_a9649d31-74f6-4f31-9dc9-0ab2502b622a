# MCDN API 设计规范

## 资源元数据使用规范

在设计新的资源类型时，务必正确使用 ResourceMetadata：

```go
type MyResource struct {
    // 使用统一的 ResourceMetadata
    Metadata ResourceMetadata `json:"metadata"`
    Spec     MyResourceSpec   `json:"spec"`
    Status   MyResourceStatus `json:"status"`
}

// 在控制器中使用元数据
func (c *Controller) handleImportedResource(resource *MyResource) error {
    // 检查是否为导入的资源
    if source, exists := resource.Metadata.GetImportSource(); exists {
        c.logger.Info("处理导入资源", "source", source, "name", resource.Metadata.Name)
        
        // 获取原始ID用于映射
        if originalID, exists := resource.Metadata.GetOriginalID(); exists {
            // 处理ID映射逻辑
        }
    }
    
    // 设置管理标识
    resource.Metadata.SetAnnotation(AnnotationManagedBy, c.name)
    
    return nil
}
```

## 注解使用最佳实践

### 1. 导入场景的标准用法

```go
// 批量导入资源时的标准做法
func (i *Importer) ImportResource(resource *MyResource, source string, originalID string) error {
    // 使用便捷方法设置导入元数据
    resource.Metadata.SetImportMetadata(source, originalID, "批量导入操作")
    
    // 设置额外的管理信息
    resource.Metadata.SetAnnotation(AnnotationCreatedBy, "import-tool")
    resource.Metadata.SetLabel("import-batch", i.batchID)
    
    return i.createResource(resource)
}
```

### 2. 生命周期管理

```go
func (c *Controller) handleResourceDeletion(resource *MyResource) error {
    // 添加终结器确保清理完成
    resource.Metadata.AddFinalizer("mcdn.lilithgames.com/cleanup-vendor")
    
    // 执行清理逻辑
    if err := c.cleanupVendorResources(resource); err != nil {
        return err
    }
    
    // 清理完成后移除终结器
    resource.Metadata.RemoveFinalizer("mcdn.lilithgames.com/cleanup-vendor")
    return nil
}
```

### 3. 资源查询和过滤

```go
// 通过标签查询资源
func (c *Controller) findResourcesByImportSource(source string) ([]*MyResource, error) {
    // 使用标签选择器查询
    labelSelector := fmt.Sprintf("import-source=%s", source)
    return c.listResourcesWithLabelSelector(labelSelector)
}

// 通过注解查询资源
func (c *Controller) findManagedResources() ([]*MyResource, error) {
    resources, err := c.listAllResources()
    if err != nil {
        return nil, err
    }
    
    var managed []*MyResource
    for _, resource := range resources {
        if _, exists := resource.Metadata.GetAnnotation(AnnotationManagedBy); exists {
            managed = append(managed, resource)
        }
    }
    return managed, nil
}
```

## 错误处理和日志记录

在处理资源元数据时，应该提供详细的上下文信息：

```go
func (c *Controller) processResource(resource *MyResource) error {
    // 构建结构化日志字段
    logFields := []any{
        "resource", resource.Metadata.Name,
        "namespace", resource.Metadata.Namespace,
    }
    
    // 添加导入相关信息到日志
    if source, exists := resource.Metadata.GetImportSource(); exists {
        logFields = append(logFields, "import-source", source)
    }
    
    c.logger.Info("开始处理资源", logFields...)
    
    if err := c.reconcileResource(resource); err != nil {
        c.logger.Error("资源处理失败", append(logFields, "error", err)...)
        return fmt.Errorf("处理资源 %s 失败: %w", resource.Metadata.Name, err)
    }
    
    c.logger.Info("资源处理成功", logFields...)
    return nil
}
```

## 资源定义最佳实践

### 标准资源结构

```go
// 遵循 Kubernetes 风格的资源定义
type ResourceSpec struct {
    // 声明式配置，描述期望状态
    DesiredState MyDesiredState `json:"desiredState"`
    
    // 配置参数
    Config MyConfig `json:"config,omitempty"`
}

type ResourceStatus struct {
    // 当前状态
    CurrentState MyCurrentState `json:"currentState,omitempty"`
    
    // 状态条件
    Conditions []ResourceCondition `json:"conditions,omitempty"`
    
    // 最后更新时间
    LastUpdateTime *time.Time `json:"lastUpdateTime,omitempty"`
}
```

### 状态条件设计

```go
type ResourceCondition struct {
    Type               string             `json:"type"`
    Status             ConditionStatus    `json:"status"`
    Reason             string             `json:"reason,omitempty"`
    Message            string             `json:"message,omitempty"`
    LastTransitionTime *time.Time         `json:"lastTransitionTime,omitempty"`
    LastUpdateTime     *time.Time         `json:"lastUpdateTime,omitempty"`
}

// 常用条件类型
const (
    ConditionReady     = "Ready"
    ConditionSynced    = "Synced"
    ConditionError     = "Error"
    ConditionPending   = "Pending"
)
```

## RESTful API 设计规范

### URL 设计

```text
GET    /api/v1/routes                    # 获取路由列表
POST   /api/v1/routes                    # 创建路由
GET    /api/v1/routes/{name}             # 获取特定路由
PUT    /api/v1/routes/{name}             # 更新路由
DELETE /api/v1/routes/{name}             # 删除路由

# 命名空间资源
GET    /api/v1/namespaces/{ns}/routes    # 获取命名空间下的路由
POST   /api/v1/namespaces/{ns}/routes    # 在命名空间中创建路由
```

### 响应格式

```go
// 单个资源响应
type ResourceResponse struct {
    APIVersion string     `json:"apiVersion"`
    Kind       string     `json:"kind"`
    Metadata   Metadata   `json:"metadata"`
    Spec       interface{} `json:"spec"`
    Status     interface{} `json:"status,omitempty"`
}

// 列表响应
type ResourceListResponse struct {
    APIVersion string            `json:"apiVersion"`
    Kind       string            `json:"kind"`
    Metadata   ListMeta          `json:"metadata"`
    Items      []ResourceResponse `json:"items"`
}
```

### 错误响应格式

```go
type ErrorResponse struct {
    APIVersion string      `json:"apiVersion"`
    Kind       string      `json:"kind"`
    Code       int         `json:"code"`
    Message    string      `json:"message"`
    Reason     string      `json:"reason,omitempty"`
    Details    interface{} `json:"details,omitempty"`
}
```

## 版本控制

### API 版本策略

1. **语义版本**：遵循 semver 规范
2. **向后兼容**：同一主版本内保持向后兼容
3. **废弃策略**：提前通知废弃的 API

### 版本定义

```go
const (
    APIVersion = "v1"
    GroupName  = "mcdn.lilithgames.com"
)

type TypeMeta struct {
    APIVersion string `json:"apiVersion"`
    Kind       string `json:"kind"`
}
```

## 验证和校验

### 输入验证

```go
type ResourceValidator interface {
    ValidateCreate(obj interface{}) error
    ValidateUpdate(oldObj, newObj interface{}) error
    ValidateDelete(obj interface{}) error
}

// 实现验证器
func (v *MyResourceValidator) ValidateCreate(obj interface{}) error {
    resource := obj.(*MyResource)
    
    // 验证必填字段
    if resource.Metadata.Name == "" {
        return fmt.Errorf("资源名称不能为空")
    }
    
    // 验证格式
    if !isValidName(resource.Metadata.Name) {
        return fmt.Errorf("资源名称格式不正确")
    }
    
    return nil
}
```

这些设计规范确保了 API 的：

- **一致性**：统一的设计风格和响应格式
- **可扩展性**：支持未来功能扩展
- **易用性**：清晰的接口和错误提示
- **兼容性**：与 Kubernetes API 风格保持一致
