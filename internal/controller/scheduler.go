package controller

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/robfig/cron/v3"

	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/logger"
)

// ScheduledTask 定义调度任务接口
type ScheduledTask interface {
	// Execute 执行任务
	Execute(ctx context.Context) error
	// GetName 获取任务名称
	GetName() string
	// GetSchedule 获取调度表达式
	GetSchedule() string
	// IsEnabled 是否启用
	IsEnabled() bool
}

// CronScheduler 定义基于 cron 的调度器
type CronScheduler struct {
	cron       *cron.Cron
	tasks      map[string]*ScheduledTaskWrapper
	tasksMutex sync.RWMutex
	logger     logger.Logger
	ctx        context.Context
	cancel     context.CancelFunc
}

// ScheduledTaskWrapper 包装调度任务
type ScheduledTaskWrapper struct {
	Task       ScheduledTask
	EntryID    cron.EntryID
	LastRun    time.Time
	NextRun    time.Time
	RunCount   int64
	ErrorCount int64
	IsActive   bool
}

// NewCronScheduler 创建新的调度器
func NewCronScheduler(logger logger.Logger) *CronScheduler {
	// 创建带有秒级精度的 cron 调度器
	cronLogger := &CronLogger{logger: logger}
	c := cron.New(cron.WithSeconds(), cron.WithLogger(cronLogger))

	ctx, cancel := context.WithCancel(context.Background())

	return &CronScheduler{
		cron:   c,
		tasks:  make(map[string]*ScheduledTaskWrapper),
		logger: logger,
		ctx:    ctx,
		cancel: cancel,
	}
}

// Start 启动调度器
func (s *CronScheduler) Start() error {
	s.logger.Info("启动 Cron 调度器")
	s.cron.Start()
	return nil
}

// Stop 停止调度器
func (s *CronScheduler) Stop() error {
	s.logger.Info("停止 Cron 调度器")
	s.cancel()

	// 停止所有任务
	ctx := s.cron.Stop()
	select {
	case <-ctx.Done():
		s.logger.Info("Cron 调度器已停止")
	case <-time.After(10 * time.Second):
		s.logger.Warn("Cron 调度器停止超时")
	}

	return nil
}

// AddTask 添加调度任务
func (s *CronScheduler) AddTask(task ScheduledTask) error {
	if !task.IsEnabled() {
		s.logger.Info("跳过禁用的任务", "task", task.GetName())
		return nil
	}

	s.tasksMutex.Lock()
	defer s.tasksMutex.Unlock()

	taskName := task.GetName()
	schedule := task.GetSchedule()

	// 如果任务已存在，先移除
	if wrapper, exists := s.tasks[taskName]; exists {
		s.cron.Remove(wrapper.EntryID)
		delete(s.tasks, taskName)
		s.logger.Info("移除已存在的任务", "task", taskName)
	}

	// 创建任务包装器
	wrapper := &ScheduledTaskWrapper{
		Task:     task,
		IsActive: true,
	}

	// 添加到 cron 调度器
	entryID, err := s.cron.AddFunc(schedule, func() {
		s.executeTask(wrapper)
	})
	if err != nil {
		return fmt.Errorf("添加任务到 cron 调度器失败: %w", err)
	}

	wrapper.EntryID = entryID
	s.tasks[taskName] = wrapper

	// 计算下次执行时间
	if entry := s.cron.Entry(entryID); entry.ID != 0 {
		wrapper.NextRun = entry.Next
	}

	s.logger.Info("成功添加调度任务",
		"task", taskName,
		"schedule", schedule,
		"nextRun", wrapper.NextRun.Format(time.RFC3339))

	return nil
}

// RemoveTask 移除调度任务
func (s *CronScheduler) RemoveTask(taskName string) error {
	s.tasksMutex.Lock()
	defer s.tasksMutex.Unlock()

	wrapper, exists := s.tasks[taskName]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskName)
	}

	s.cron.Remove(wrapper.EntryID)
	delete(s.tasks, taskName)

	s.logger.Info("成功移除调度任务", "task", taskName)
	return nil
}

// UpdateTask 更新调度任务
func (s *CronScheduler) UpdateTask(task ScheduledTask) error {
	// 简单的实现：先移除再添加
	taskName := task.GetName()

	// 移除旧任务（如果存在）
	_ = s.RemoveTask(taskName) // 忽略错误，可能任务不存在

	// 添加新任务
	return s.AddTask(task)
}

// GetTask 获取任务信息
func (s *CronScheduler) GetTask(taskName string) (*ScheduledTaskWrapper, bool) {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()

	wrapper, exists := s.tasks[taskName]
	return wrapper, exists
}

// ListTasks 列出所有任务
func (s *CronScheduler) ListTasks() map[string]*ScheduledTaskWrapper {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()

	// 返回副本
	result := make(map[string]*ScheduledTaskWrapper, len(s.tasks))
	for name, wrapper := range s.tasks {
		result[name] = wrapper
	}

	return result
}

// executeTask 执行任务
func (s *CronScheduler) executeTask(wrapper *ScheduledTaskWrapper) {
	taskName := wrapper.Task.GetName()

	// 检查任务是否仍然活跃
	if !wrapper.IsActive || !wrapper.Task.IsEnabled() {
		s.logger.Debug("跳过非活跃任务", "task", taskName)
		return
	}

	wrapper.LastRun = time.Now()
	wrapper.RunCount++

	s.logger.Info("开始执行调度任务", "task", taskName, "runCount", wrapper.RunCount)

	// 创建执行上下文，设置超时
	ctx, cancel := context.WithTimeout(s.ctx, 30*time.Minute) // 默认30分钟超时
	defer cancel()

	// 执行任务
	if err := wrapper.Task.Execute(ctx); err != nil {
		wrapper.ErrorCount++
		s.logger.Error("调度任务执行失败",
			"task", taskName,
			"error", err,
			"errorCount", wrapper.ErrorCount)
	} else {
		s.logger.Info("调度任务执行成功",
			"task", taskName,
			"duration", time.Since(wrapper.LastRun))
	}

	// 更新下次执行时间
	if entry := s.cron.Entry(wrapper.EntryID); entry.ID != 0 {
		wrapper.NextRun = entry.Next
	}
}

// GetStats 获取调度器统计信息
func (s *CronScheduler) GetStats() map[string]any {
	s.tasksMutex.RLock()
	defer s.tasksMutex.RUnlock()

	stats := map[string]any{
		"totalTasks":  len(s.tasks),
		"activeTasks": 0,
		"totalRuns":   int64(0),
		"totalErrors": int64(0),
		"tasks":       make([]map[string]any, 0, len(s.tasks)),
	}

	for name, wrapper := range s.tasks {
		if wrapper.IsActive {
			stats["activeTasks"] = stats["activeTasks"].(int) + 1
		}
		stats["totalRuns"] = stats["totalRuns"].(int64) + wrapper.RunCount
		stats["totalErrors"] = stats["totalErrors"].(int64) + wrapper.ErrorCount

		taskInfo := map[string]any{
			"name":       name,
			"schedule":   wrapper.Task.GetSchedule(),
			"enabled":    wrapper.Task.IsEnabled(),
			"isActive":   wrapper.IsActive,
			"runCount":   wrapper.RunCount,
			"errorCount": wrapper.ErrorCount,
			"lastRun":    wrapper.LastRun,
			"nextRun":    wrapper.NextRun,
		}
		stats["tasks"] = append(stats["tasks"].([]map[string]any), taskInfo)
	}

	return stats
}

// CronLogger 实现 cron 包的 Logger 接口
type CronLogger struct {
	logger logger.Logger
}

// Printf 实现 Printf 接口，用于 cron 包的日志输出
func (l *CronLogger) Printf(format string, args ...any) {
	// 使用结构化日志
	l.logger.Info(fmt.Sprintf(format, args...))
}

// Info 记录信息日志
func (l *CronLogger) Info(msg string, keysAndValues ...any) {
	l.logger.Info(msg, keysAndValues...)
}

// Error 记录错误日志
func (l *CronLogger) Error(err error, msg string, keysAndValues ...any) {
	args := append([]any{"error", err}, keysAndValues...)
	l.logger.Error(msg, args...)
}
