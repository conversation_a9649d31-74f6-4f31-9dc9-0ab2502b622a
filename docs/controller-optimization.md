# Controller-Manager 优化方案

## 优化内容

基于你的建议，我们对 Controller-Manager 设计进行了两个重要优化：

### 1. 简化 SetupWithManager 参数

**之前：** 需要传递多个参数

```go
func (c *Controller) SetupWithManager(mgr *controller.Manager, vendorMgr vendor.Manager) error
```

**现在：** 只需要一个 manager 参数，所有依赖从 manager 获取

```go
func (c *Controller) SetupWithManager(mgr controller.ManagerInterface) error
```

**改进点：**

- ✅ 更加符合依赖注入原则
- ✅ Manager 承担了依赖提供者的责任
- ✅ 控制器不需要关心依赖的创建和管理
- ✅ 接口化设计便于测试和扩展

### 2. 类型安全的 For() 方法

**之前：** 使用 magic string

```go
controller.NewControllerManagedBy(mgr).
    Named("route-controller").
    For("route").  // ❌ magic string，容易出错
    Complete(controller)
```

**现在：** 支持类型安全的资源定义

```go
// 方式一：使用具体资源实例（推荐）
controller.NewControllerManagedBy(mgr).
    Named("route-controller").
    For(&apiv1.RouteResource{}).  // ✅ 类型安全
    Complete(controller)

// 方式二：K8s 风格的 nil 指针语法
controller.NewControllerManagedBy(mgr).
    Named("route-controller").
    For((*apiv1.RouteResource)(nil)).  // ✅ 类似 K8s
    Complete(controller)

// 方式三：向下兼容的字符串方式
controller.NewControllerManagedBy(mgr).
    Named("route-controller").
    For("route").  // ✅ 仍然支持，但不推荐
    Complete(controller)
```

## 完整的使用示例

### 设置控制器

```go
package main

import (
    apiv1 "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/api/v1"
    "gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/controller"
    "gitlab.lilithgames/gits/lilithgame.com/yunwei/lilith-cdn/mcdn/internal/resource/policy"
)

func setupControllers(etcdClient *clientv3.Client, log logger.Logger, vendorMgr vendor.Manager) error {
    // 创建管理器 - 所有依赖都在这里注入
    manager := controller.NewManager(etcdClient, log, vendorMgr)

    // 方式一：最简洁的语法糖
    routeController := route.NewController(etcdClient, vendorMgr, log)
    if err := routeController.SetupWithManager(manager); err != nil {
        return fmt.Errorf("设置 Route 控制器失败: %w", err)
    }

    // 方式二：类型安全的 Builder 模式
    routeController2 := route.NewController(etcdClient, vendorMgr, log)
    err := controller.NewControllerManagedBy(manager).
        Named("route-controller-v2").
        For(&apiv1.RouteResource{}).  // 类型安全！
        Complete(routeController2)
    if err != nil {
        return fmt.Errorf("设置控制器失败: %w", err)
    }

    // 方式三：K8s 风格
    routeController3 := route.NewController(etcdClient, vendorMgr, log)
    err = controller.NewControllerManagedBy(manager).
        Named("route-controller-v3").
        For((*apiv1.RouteResource)(nil)).  // K8s 风格！
        Complete(routeController3)
    if err != nil {
        return fmt.Errorf("设置控制器失败: %w", err)
    }

    return nil
}
```

### 资源定义

```go
// api/v1/resource.go
package v1

// Resource 接口定义
type Resource interface {
    GetKind() string
    GetEtcdPrefix() string
}

// RouteResource 实现了 Resource 接口
type RouteResource struct {
    *Route
}

func (r *RouteResource) GetKind() string {
    return "Route"
}

func (r *RouteResource) GetEtcdPrefix() string {
    return "/mcdn/policy/"
}

// 编译时验证接口实现
var _ Resource = (*RouteResource)(nil)
```

### For() 方法的智能类型推断

```go
// For() 方法支持多种输入类型：

// 1. 具体资源实例
.For(&apiv1.RouteResource{})

// 2. nil 指针（K8s 风格）
.For((*apiv1.RouteResource)(nil))

// 3. 字符串（向下兼容）
.For("route")

// 4. 反射推断（未来可扩展）
// .For(reflect.TypeOf(apiv1.RouteResource{}))
```

## 架构优势

### 1. 依赖注入优化

**Manager 作为依赖提供者：**

```go
type ManagerInterface interface {
    RegisterController(controller Controller) error
    GetEtcdClient() *clientv3.Client
    GetLogger() logger.Logger
    GetVendorManager() vendor.Manager
}
```

**控制器专注业务逻辑：**

```go
// 控制器无需关心依赖的创建
func (c *Controller) SetupWithManager(mgr ManagerInterface) error {
    return controller.NewControllerManagedBy(mgr).
        Named("route-controller").
        For(&apiv1.RouteResource{}).
        Complete(c)
}
```

### 2. 类型安全保证

**编译时检查：**

- ✅ 资源类型在编译时验证
- ✅ IDE 自动补全和重构支持
- ✅ 避免 magic string 带来的运行时错误

**运行时优化：**

- ✅ 自动推断 etcd 前缀
- ✅ 统一的资源类型管理
- ✅ 更好的错误信息

### 3. 接口设计改进

**ManagerInterface vs 具体 Manager：**

- ✅ 便于单元测试（可以 mock）
- ✅ 支持不同的 Manager 实现
- ✅ 更好的解耦和依赖控制

## 对比 Kubernetes

| 特性 | Kubernetes | 优化前 | 优化后 |
|------|------------|--------|--------|
| 参数数量 | `SetupWithManager(mgr)` | `SetupWithManager(mgr, vendor)` | `SetupWithManager(mgr)` ✅ |
| 类型安全 | `For(&v1.Pod{})` | `For("route")` | `For(&RouteResource{})` ✅ |
| 依赖注入 | Manager 提供所有依赖 | 手动传递依赖 | Manager 提供所有依赖 ✅ |
| 接口设计 | `ctrl.Manager` 接口 | 具体 `*Manager` 类型 | `ManagerInterface` 接口 ✅ |

## 向后兼容性

优化后的设计完全支持向后兼容：

```go
// 新的推荐方式
routeController.SetupWithManager(manager)

// 旧的方式仍然可用
manager.RegisterController(routeController)

// 字符串方式仍然支持（但不推荐）
.For("route")
```

## 总结

这次优化解决了你提到的两个核心问题：

1. **参数简化**：`SetupWithManager` 现在只需要一个参数，符合依赖注入最佳实践
2. **类型安全**：`For()` 方法支持类型安全的资源定义，避免 magic string

同时保持了：

- ✅ 向后兼容性
- ✅ 灵活的使用方式
- ✅ 清晰的代码结构
- ✅ 良好的可测试性

这样的设计更加接近 Kubernetes 的优雅风格，同时适配了我们项目的具体需求！
