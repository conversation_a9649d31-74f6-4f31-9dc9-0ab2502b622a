# Taskfile 使用说明

本项目使用 [Task](https://taskfile.dev/) 作为构建工具。

## 安装 Task

```bash
# macOS
brew install go-task/tap/go-task

# 或者使用 go install
go install github.com/go-task/task/v3/cmd/task@latest
```

## 主要任务

### 构建相关

- `task build` - 构建优化的二进制文件（已启用 `-ldflags="-s -w"` 去除调试信息和符号表）
- `task build-compress` - 构建并使用 UPX 压缩（需要安装 UPX）
- `task clean` - 清理构建产物

### 开发相关

- `task dev` - 开发模式运行（不构建二进制文件）
- `task run` - 构建并运行
- `task format` - 格式化代码（goimports + gofumpt）
- `task lint` - 运行代码检查
- `task test` - 运行测试

### 其他

- `task install-tools` - 安装开发工具
- `task tidy` - 整理 Go modules
- `task all` - 执行完整的构建流程

## 构建优化说明

`task build` 使用了以下优化参数来减小二进制文件大小：

1. `CGO_ENABLED=0` - 禁用 CGO，生成静态链接的二进制文件
2. `-a` - 强制重新构建所有包
3. `-installsuffix cgo` - 避免与启用 CGO 的包冲突
4. `-ldflags="-s -w"` - 去除符号表和调试信息
   - `-s` - 去除符号表
   - `-w` - 去除调试信息

### 进一步优化

如果需要更小的二进制文件，可以：

1. 安装 UPX 压缩工具：

   ```bash
   # macOS
   brew install upx
   
   # Ubuntu/Debian
   apt install upx-ucl
   ```

2. 使用 UPX 压缩：

   ```bash
   task build-compress
   ```

UPX 通常可以将二进制文件大小压缩 50-70%。

## 版本信息

构建时会自动注入版本信息：

- Version: Git 标签或提交 hash
- Commit: Git 提交 hash (短)
- BuildTime: 构建时间 (UTC)
