package v1

// Resource 定义所有可管理资源需要实现的接口
// 类似于 K8s 的 client.Object，提供运行时类型信息
type Resource interface {
	// GetKind 返回资源类型，如 "Policy", "DNSZone", "Distribution"
	GetKind() string
	// GetEtcdPrefix 返回该资源在 etcd 中的前缀路径
	GetEtcdPrefix() string
}

// PolicyResource 路由策略资源，实现 Resource 接口
type PolicyResource struct {
	// 嵌入实际的 Policy 结构
	*Policy
}

// NewPolicyResource 创建路由资源包装器
func NewPolicyResource(policy *Policy) *PolicyResource {
	if policy == nil {
		policy = &Policy{}
	}
	return &PolicyResource{Policy: policy}
}

// GetKind 返回资源类型
func (r *PolicyResource) GetKind() string {
	return "Policy"
}

// GetEtcdPrefix 返回 etcd 前缀
func (r *PolicyResource) GetEtcdPrefix() string {
	return "/mcdn/policy/"
}

// 编译时验证 PolicyResource 实现了 Resource 接口
var _ Resource = (*PolicyResource)(nil)

// DNSRecordResource DNS 记录资源，实现 Resource 接口
type DNSRecordResource struct {
	*DNSRecord
}

// NewDNSRecordResource 创建 DNS 记录资源包装器
func NewDNSRecordResource(record *DNSRecord) *DNSRecordResource {
	if record == nil {
		record = &DNSRecord{}
	}
	return &DNSRecordResource{DNSRecord: record}
}

// GetKind 返回资源类型
func (r *DNSRecordResource) GetKind() string {
	return "DNSRecord"
}

// GetEtcdPrefix 返回 etcd 前缀
func (r *DNSRecordResource) GetEtcdPrefix() string {
	return "/mcdn/dns/records/"
}

// DistributionResource 分发策略资源，实现 Resource 接口
type DistributionResource struct {
	*Distribution
}

// NewDistributionResource 创建分发策略资源包装器
func NewDistributionResource(distribution *Distribution) *DistributionResource {
	if distribution == nil {
		distribution = &Distribution{}
	}
	return &DistributionResource{Distribution: distribution}
}

// GetKind 返回资源类型
func (r *DistributionResource) GetKind() string {
	return "Distribution"
}

// GetEtcdPrefix 返回 etcd 前缀
func (r *DistributionResource) GetEtcdPrefix() string {
	return "/mcdn/distribution/"
}

// 编译时验证所有资源都实现了 Resource 接口
var (
	_ Resource = (*DNSRecordResource)(nil)
	_ Resource = (*DistributionResource)(nil)
	_ Resource = (*PolicyResource)(nil)
)
