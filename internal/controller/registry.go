package controller

import (
	"context"
	"fmt"
	"sync"

	clientv3 "go.etcd.io/etcd/client/v3"

	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/logger"
	"gitlab.lilithgame.com/yunwei/lilith-cdn/mcdn/internal/vendor"
)

// Manager 控制器管理器
type Manager struct {
	etcdClient  *clientv3.Client
	logger      logger.Logger
	vendorMgr   vendor.Manager
	controllers map[string]Controller
	mu          sync.RWMutex
	started     bool
}

// NewManager 创建控制器管理器
func NewManager(etcdClient *clientv3.Client, logger logger.Logger, vendorMgr vendor.Manager) *Manager {
	return &Manager{
		etcdClient:  etcdClient,
		logger:      logger,
		vendorMgr:   vendorMgr,
		controllers: make(map[string]Controller),
	}
}

// GetEtcdClient 获取 etcd 客户端
func (m *Manager) GetEtcdClient() *clientv3.Client {
	return m.etcdClient
}

// GetLogger 获取日志记录器
func (m *Manager) GetLogger() logger.Logger {
	return m.logger
}

// GetVendorManager 获取 vendor 管理器
func (m *Manager) GetVendorManager() vendor.Manager {
	return m.vendorMgr
}

// RegisterController 注册控制器
func (m *Manager) RegisterController(ctrl Controller) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	name := ctrl.GetName()
	if _, exists := m.controllers[name]; exists {
		return fmt.Errorf("控制器 %s 已存在", name)
	}

	m.controllers[name] = ctrl
	m.logger.Info("注册控制器", "name", name, "resourceType", ctrl.GetResourceType())
	return nil
}

// UnregisterController 注销控制器
func (m *Manager) UnregisterController(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	controller, exists := m.controllers[name]
	if !exists {
		return fmt.Errorf("控制器 %s 不存在", name)
	}

	if m.started {
		if err := controller.Stop(); err != nil {
			m.logger.Error("停止控制器失败", "name", name, "error", err)
		}
	}

	delete(m.controllers, name)
	m.logger.Info("注销控制器", "name", name)
	return nil
}

// GetController 获取控制器
func (m *Manager) GetController(name string) (Controller, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	controller, exists := m.controllers[name]
	return controller, exists
}

// ListControllers 列出所有控制器
func (m *Manager) ListControllers() []Controller {
	m.mu.RLock()
	defer m.mu.RUnlock()

	controllers := make([]Controller, 0, len(m.controllers))
	for _, controller := range m.controllers {
		controllers = append(controllers, controller)
	}
	return controllers
}

// Start 启动所有控制器
func (m *Manager) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.started {
		return fmt.Errorf("控制器管理器已启动")
	}

	m.logger.Info("启动控制器管理器", "controllerCount", len(m.controllers))

	// 启动所有控制器
	for name, controller := range m.controllers {
		m.logger.Info("启动控制器", "name", name)
		if err := controller.Start(ctx); err != nil {
			m.logger.Error("启动控制器失败", "name", name, "error", err)
			// 停止已启动的控制器
			m.stopStartedControllers()
			return fmt.Errorf("启动控制器 %s 失败: %w", name, err)
		}
	}

	m.started = true
	m.logger.Info("控制器管理器启动完成")

	// 等待上下文取消
	<-ctx.Done()
	m.logger.Info("控制器管理器收到停止信号")

	// 停止所有控制器
	m.stopStartedControllers()
	m.started = false

	return nil
}

// stopStartedControllers 停止已启动的控制器
func (m *Manager) stopStartedControllers() {
	for name, controller := range m.controllers {
		m.logger.Info("停止控制器", "name", name)
		if err := controller.Stop(); err != nil {
			m.logger.Error("停止控制器失败", "name", name, "error", err)
		}
	}
}

// IsStarted 检查管理器是否已启动
func (m *Manager) IsStarted() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.started
}

// RegisterControllers 注册所有资源控制器
func RegisterControllers(manager *Manager, vendorMgr vendor.Manager) error {
	// 注册 Route 控制器 (支持基于 cron 的调度)
	// 注意：由于循环依赖问题，暂时注释掉。具体控制器将在commands中直接创建
	// routeController := route.NewController(manager.etcdClient, vendorMgr, manager.logger)
	// if err := manager.RegisterController(routeController); err != nil {
	//     return fmt.Errorf("注册 Route 控制器失败: %w", err)
	// }

	// 示例：注册 DNS 控制器
	// dnsController := dns.NewController(manager.etcdClient, manager.logger)
	// if err := manager.RegisterController(dnsController); err != nil {
	//     return fmt.Errorf("注册 DNS 控制器失败: %w", err)
	// }

	// 示例：注册 Distribution 控制器
	// distributionController := distribution.NewController(manager.etcdClient, vendorMgr, manager.logger)
	// if err := manager.RegisterController(distributionController); err != nil {
	//     return fmt.Errorf("注册 Distribution 控制器失败: %w", err)
	// }

	manager.logger.Info("所有控制器注册完成")
	return nil
}
