package worker

import (
	"context"
	"time"

	"gitlab.lilithgame.com/yunwei/pkg/logger"
)

type Options struct {
	Env string
}

type Worker struct {
	opts Options
	jobs chan struct{}
}

func New(opts Options) *Worker {
	return &Worker{opts: opts, jobs: make(chan struct{}, 1)}
}

func (w *Worker) Run(ctx context.Context) error {
	logger.Info("worker 开始运行", "env", w.opts.Env)
	// 示例任务循环
	for {
		select {
		case <-ctx.Done():
			return nil
		case <-time.After(5 * time.Second):
			continue
		}
	}
}

func (w *Worker) Shutdown(ctx context.Context) error {
	logger.Info("worker 关闭中")
	return nil
}
